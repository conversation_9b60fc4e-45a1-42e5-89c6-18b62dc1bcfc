module.exports = {
    apps: [
        {
            name: 'twilio-gemini-backend',
            script: './node_modules/.bin/tsx',
            args: 'index.ts',
            exec_mode: 'cluster',
            instances: 4,
            max_memory_restart: '1G',
            max_restarts: 10,
            min_uptime: '10s',
            error_file: './logs/backend-error.log',
            out_file: './logs/backend-out.log',
            merge_logs: true,
            time: true,
            env_file: '.env',
            env: {
                PORT: 3101,
                NODE_ENV: 'production',
                PUBLIC_URL: 'https://gemini-api.verduona.com',
                CORS_ORIGIN: 'https://twilio-gemini.verduona.com',
                // Load sensitive credentials from the environment
                GEMINI_API_KEY: process.env.GEMINI_API_KEY,
                GEMINI_DEFAULT_MODEL: 'gemini-2.0-flash-live-001',
                TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID,
                TWILIO_AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN,
                TWILIO_PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER,
                VOICE_AOEDE: 'Aoede, female, bright neutral narrator',
                VOICE_PUCK: 'Puck, male, lively higher tenor',
                VOICE_CHARON: 'Charon, male, deep warm baritone',
                VOICE_KORE: 'Kore, female, soft alto empathetic',
                VOICE_FENRIR: 'Fenrir, male, assertive mid-range',
                VOICE_LEDA: 'Leda, female, clear RP-style announcer',
                VOICE_ORUS: 'Orus, male, relaxed breathy tenor',
                VOICE_ZEPHYR: 'Zephyr, female, airy youthful soprano'
            }
        },
        {
            name: 'twilio-gemini-frontend',
            cwd: './call-center-frontend',
            script: 'npm',
            args: 'start',
            env: {
                PORT: 3011,
                NODE_ENV: 'production',
                NEXT_PUBLIC_BACKEND_URL: 'https://gemini-api.verduona.com'
            }
        }
    ]
};
