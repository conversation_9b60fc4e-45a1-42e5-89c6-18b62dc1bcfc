import { describe, it } from 'node:test';
import assert from 'node:assert';
import { routeGeminiMessage } from '../src/session/message-router';

describe('MessageRouter', () => {
    it('routes text messages and updates summary', async () => {
        const connectionData = {
            conversationLog: [],
            summaryRequested: true,
            summaryText: '',
            sessionType: 'twilio_call'
        };

        const message = {
            serverContent: {
                modelTurn: {
                    parts: [{ text: 'Hello there', inlineData: { mimeType: 'audio/mpeg', data: 'abc' } }]
                }
            }
        };

        let forwarded = false;
        const metrics = new Map([
            ['call1', { messagesReceived: 0, lastActivity: 0 }]
        ]);

        await routeGeminiMessage('call1', message as any, connectionData as any, {
            audioProcessor: {} as any,
            activeConnections: new Map(),
            sessionMetrics: metrics,
            forwardAudioFn: async () => { forwarded = true; return true; }
        });

        assert.strictEqual(connectionData.conversationLog.length, 1);
        assert.ok(connectionData.summaryText.includes('Hello'));
        assert.ok(forwarded);
        assert.strictEqual(metrics.get('call1').messagesReceived, 1);
    });

    it('buffers audio when session not ready', async () => {
        const connectionData = {
            conversationLog: [],
            summaryRequested: false,
            summaryText: '',
            sessionType: 'twilio_call',
            sessionReady: false,
            audioForwardingEnabled: false
        };

        const message = {
            serverContent: {
                modelTurn: {
                    parts: [{ inlineData: { mimeType: 'audio/mpeg', data: 'abc' } }]
                }
            }
        };

        let forwarded = false;

        await routeGeminiMessage('call2', message as any, connectionData as any, {
            audioProcessor: {} as any,
            activeConnections: new Map(),
            sessionMetrics: new Map(),
            forwardAudioFn: async () => { forwarded = true; return true; }
        });

        assert.ok(forwarded);
    });
});
