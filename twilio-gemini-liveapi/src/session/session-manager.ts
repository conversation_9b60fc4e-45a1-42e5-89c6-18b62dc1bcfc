import { Modality } from '../gemini/client';
import { AudioProcessor } from '../audio/audio-processor';
import { forwardAudio, initializeAudioForwarding, cleanupAudioForwarding } from '../audio/audio-forwarding';
import { routeGeminiMessage } from './message-router';
import { generateLocalSummary } from './summary-generator';
import { standardizeConnectionData } from '../utils/websocket-utils';
import { timerManager } from '../utils/timer-manager';
import { sessionLogger } from '../utils/logger';
import { BoundedMap, BoundedSet, SessionMetrics } from './metrics';
import { sendAudioToGemini as routeSendAudioToGemini, sendBrowserAudioToGemini as routeSendBrowserAudioToGemini, setupTwilioReadinessCheck as routingSetupTwilioReadinessCheck, bufferEarlyAudio as routingBufferEarlyAudio, processBuffered<PERSON>udio as routingProcessBufferedAudio } from './websocket-routing';
import { recoverSession as recoveryHandler } from './recovery';
import {
    ConnectionData,
    GeminiSession,
    GeminiClient,
    GeminiLiveMessage,
    GeminiRealtimeInput,
    GeminiError,
    ConversationEntry,
    TranscriptEntry,
    SpeechTranscriptEntry
} from '../types/global';
import { ContextManager } from './context-manager';

interface SessionConfig {
    model: string;
    voice: string;
    aiInstructions?: string;
    sessionType?: string;
    isIncomingCall?: boolean;
    scriptId?: string | null;
    campaignId?: string | null;
    scriptType?: string;
    targetName?: string;
    targetPhoneNumber?: string;
}

export type { SessionMetrics } from './metrics';
export { BoundedMap, BoundedSet } from './metrics';

interface ExtendedConnectionData extends ConnectionData {
    sessionConfig?: SessionConfig;
    sessionReady?: boolean;
    sessionInitialized?: number;
    geminiSessionError?: string;
    sessionActivatedAt?: number;
    fullyReady?: boolean;
    // Other properties are inherited from ConnectionData
    isTwilioCall?: boolean;
    streamSid?: string;
}

// Bounded Map and Set for memory safety

// Session Manager for Gemini connections with recovery
export class SessionManager {
    private contextManager: ContextManager;
    private geminiClient: GeminiClient;
    private recoveryInProgress: BoundedSet<string>;
    private audioProcessor: AudioProcessor;
    private sessionMetrics: BoundedMap<string, SessionMetrics>;
    private activeConnections: Map<string, ConnectionData> | null;
    private earlyAudioBuffers: Map<string, Buffer[]>;
    private cleanupLocks: Map<string, Promise<void>>;

    constructor(
        contextManager: ContextManager,
        geminiClient: GeminiClient,
        activeConnections: Map<string, ConnectionData> | null = null
    ) {
        this.contextManager = contextManager;
        this.geminiClient = geminiClient;
        this.recoveryInProgress = new BoundedSet<string>(500); // Increased from 200 to 500 for better recovery tracking
        this.audioProcessor = new AudioProcessor();
        this.sessionMetrics = new BoundedMap<string, SessionMetrics>(2000); // Increased from 1000 to 2000 for better metrics retention
        this.activeConnections = activeConnections;
        this.earlyAudioBuffers = new Map(); // Buffer for early audio packets // Reference to activeConnections for audio forwarding
        this.cleanupLocks = new Map();
    }


    // Create new Gemini session
    async createGeminiSession(
        callSid: string,
        config: SessionConfig,
        connectionData: ConnectionData
    ): Promise<GeminiSession | null> {
        const sessionStartTime = Date.now();
        const extConnectionData = connectionData as ExtendedConnectionData;
        
        try {
            sessionLogger.info(`🤖 [${callSid}] Creating Gemini session with model: ${config.model}, voice: ${config.voice}`);
            sessionLogger.info(`🔍 [${callSid}] ===== SESSION MANAGER MODEL DEBUG =====`);
            sessionLogger.info(`🔍 [${callSid}] config.model = "${config.model}"`);
            sessionLogger.info(`🔍 [${callSid}] config.voice = "${config.voice}"`);
            sessionLogger.info(`🔍 [${callSid}] process.env.GEMINI_DEFAULT_MODEL = "${process.env.GEMINI_DEFAULT_MODEL}"`);
            sessionLogger.info(`🔍 [${callSid}] ==========================================`);

            // CRITICAL VALIDATION: Verify AI instructions are present and sufficient before creating session
            if (!config.aiInstructions || config.aiInstructions.trim().length === 0) {
                const errorMessage = `Cannot create Gemini session: AI instructions are missing or empty`;
                sessionLogger.error(`❌ [${callSid}] VALIDATION FAILED: ${errorMessage}`);
                sessionLogger.error(`❌ [${callSid}] Session config validation:`, {
                    hasConfig: !!config,
                    hasAiInstructions: !!config.aiInstructions,
                    instructionLength: config.aiInstructions?.length || 0,
                    sessionType: config.sessionType,
                    scriptId: config.scriptId
                });

                // Mark session as failed and return early
                extConnectionData.isSessionActive = false;
                extConnectionData.geminiSessionError = errorMessage;

                throw new Error(errorMessage);
            }

            // Additional validation: Check instruction length
            if (config.aiInstructions.trim().length < 100) {
                const errorMessage = `AI instructions too short (${config.aiInstructions.length} chars) - minimum 100 characters required`;
                sessionLogger.error(`❌ [${callSid}] VALIDATION FAILED: ${errorMessage}`);

                extConnectionData.isSessionActive = false;
                extConnectionData.geminiSessionError = errorMessage;

                throw new Error(errorMessage);
            }
            
            sessionLogger.info(`✅ [${callSid}] AI instructions validation passed (${config.aiInstructions.length} characters)`);
            const validationPreview = config.aiInstructions.substring(0, 200);
            sessionLogger.info(`🎯 [${callSid}] Instructions preview: ${validationPreview}...`);

            // Use arrow functions to maintain 'this' context instead of aliasing

            // Initialize session metrics BEFORE creating session to avoid race conditions
            this.sessionMetrics.set(callSid, {
                startTime: Date.now(),
                messagesReceived: 0,
                messagesSent: 0,
                recoveryCount: 0,
                lastActivity: Date.now(),
                isInitializing: true
            });

            // Store reference immediately to prevent race conditions
            extConnectionData.geminiSession = undefined;
            extConnectionData.isSessionActive = false;

            // Store config for callback access
            extConnectionData.sessionConfig = config;

            // Create session and store reference immediately
            // Add call direction context to system instructions
            let systemInstruction = config.aiInstructions;
            sessionLogger.info(`🎯 [${callSid}] ===== SYSTEM INSTRUCTION DEBUG =====`);
            sessionLogger.info(`🎯 [${callSid}] Has config: ${!!config}`);
            sessionLogger.info(`🎯 [${callSid}] Has AI instructions: ${!!config.aiInstructions}`);
            sessionLogger.info(`🎯 [${callSid}] Original instruction length: ${config.aiInstructions?.length || 0}`);
            sessionLogger.info(`🎯 [${callSid}] Final instruction length: ${systemInstruction?.length || 0}`);
            sessionLogger.info(`🎯 [${callSid}] Script ID: ${config.scriptId || 'NONE'}`);
            sessionLogger.info(`🎯 [${callSid}] Campaign ID: ${config.campaignId || 'NONE'}`);
            sessionLogger.info(`🎯 [${callSid}] Script type: ${config.scriptType || 'NONE'}`);
            sessionLogger.info(`🎯 [${callSid}] Session type: ${config.sessionType || 'NONE'}`);
            sessionLogger.info(`🎯 [${callSid}] Is incoming call: ${config.isIncomingCall || false}`);
            const instructionPreview = systemInstruction?.substring(0, 300) || 'NO INSTRUCTIONS';
            sessionLogger.info(`🎯 [${callSid}] First 300 chars: ${instructionPreview}...`);
            sessionLogger.info(`🎯 [${callSid}] ====================================`);

            // Add context based on call direction for Twilio calls
            if (config.sessionType === 'twilio_call' && systemInstruction) {
                if (!config.isIncomingCall) {
                    // Outbound calls: AI initiates the conversation
                    systemInstruction = `${systemInstruction}\n\n` +
                        'You are making an outbound call. When the call connects, introduce yourself and ' +
                        'begin the conversation according to the script above. Start speaking immediately when the call is answered.';
                } else {
                    // Inbound calls: AI should greet the caller and wait for their response
                    systemInstruction = `${systemInstruction}\n\n` +
                        'You are receiving an inbound call. When the customer speaks, greet them warmly and ' +
                        'assist them according to the script above.';
                }
            }

            // Fallback for inbound calls without instructions
            if (!systemInstruction && config.isIncomingCall && config.sessionType === 'twilio_call') {
                sessionLogger.warn(`No AI instructions found for inbound call, using fallback`);
                systemInstruction = 'You are a helpful customer service representative. ' +
                    'Greet the caller warmly and ask how you can help them today.';
            }

            // CRITICAL DEBUG: Log full configuration being sent to Gemini
            sessionLogger.info(`🔍 [${callSid}] Attempting Gemini connection with config:`, {
                model: config.model,
                voice: config.voice,
                sessionType: config.sessionType,
                isIncomingCall: config.isIncomingCall,
                hasSystemInstruction: !!systemInstruction,
                instructionLength: systemInstruction?.length,
                hasGeminiClient: !!this.geminiClient,
                hasLiveAPI: !!(this.geminiClient && 'live' in this.geminiClient)
            });

            if (systemInstruction) {
                sessionLogger.info(`🎯 [${callSid}] System instruction preview: ${systemInstruction.substring(0, 300)}...`);
            } else {
                sessionLogger.error(`❌ [${callSid}] CRITICAL: No system instruction available!`);
            }

            sessionLogger.info(`🚀 [${callSid}] Connecting to Gemini Live API with system instruction...`);
            const geminiSession = await (this.geminiClient as any).live.connect({
                model: config.model,
                systemInstruction: systemInstruction ? {
                    parts: [{
                        text: systemInstruction
                    }]
                } : undefined,
                generationConfig: {
                    responseModalities: ['AUDIO'],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                callbacks: {
                    onopen: () => {
                        sessionLogger.info(`✅ [${callSid}] Gemini session opened`);

                        // CRITICAL: Don't immediately mark as active - validate first
                        sessionLogger.info(`🔄 [${callSid}] Validating session before activation...`);

                        // Update metrics state
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.isInitializing = false;
                        }

                        // Save initial context with bounded arrays
                        this.contextManager.saveSessionContext(callSid, {
                            ...config,
                            ...extConnectionData,
                            conversationLog: [],
                            fullTranscript: [],
                            speechTranscript: [], // Initialize bounded speech transcript
                            maxConversationLogSize: 1000, // Increased from 500 to 1000 for better conversation retention
                            maxTranscriptSize: 2000, // Increased from 1000 to 2000 for better transcript retention
                            maxSpeechTranscriptSize: 2000 // Increased from 1000 to 2000 for better speech transcript retention
                        });

                        // CRITICAL FIX: Live API setup is now handled in initial connection configuration
                        sessionLogger.info(`✅ [${callSid}] Live API setup configuration included in connection`);
                        if (config.aiInstructions) {
                            const instructionSnippet = config.aiInstructions.substring(0, 200);
                            sessionLogger.info(`🎯 [${callSid}] AI instructions included: ${instructionSnippet}...`);
                        } else {
                            sessionLogger.warn(`⚠️ [${callSid}] No AI instructions in config - session may not work properly`);
                        }

                        // Validate session setup
                        sessionLogger.info(`🔍 [${callSid}] Session validation:`, {
                            hasSystemInstruction: !!systemInstruction,
                            instructionLength: systemInstruction?.length,
                            model: config.model,
                            voice: config.voice,
                            hasGeminiSession: !!extConnectionData.geminiSession
                        });

                        // CRITICAL: Mark as active immediately after validation - NO DELAYS
                        if (extConnectionData.geminiSession && !extConnectionData.geminiSessionError) {
                            extConnectionData.isSessionActive = true;
                            extConnectionData.sessionActivatedAt = Date.now();

                            // Check if Twilio is also connected for full readiness
                            if (extConnectionData.twilioConnected) {
                                extConnectionData.fullyReady = true;
                                sessionLogger.info(`🎯 [${callSid}] Session fully ready - both Gemini and Twilio connected`);
                            } else {
                                sessionLogger.info(`🔄 [${callSid}] Gemini session active, waiting for Twilio connection`);
                                // Set up a check to mark as fully ready when Twilio connects
                                this.setupTwilioReadinessCheck(callSid, extConnectionData);
                            }

                            sessionLogger.info(`✅ [${callSid}] Session activated and ready for audio processing`);
                        } else {
                            sessionLogger.error(`❌ [${callSid}] Session validation failed during activation`);
                            extConnectionData.geminiSessionError = 'Session validation failed';
                        }

                        sessionLogger.info(`🔄 [${callSid}] Session initialized, activation complete`);
                    },

                    onerror: (error: GeminiError) => {
                        sessionLogger.error(`❌ [${callSid}] Gemini session error:`, error instanceof Error ? error : new Error(String(error)));
                        this.contextManager.markSessionInterrupted(callSid, 'session_error');

                        // Update metrics
                        const metrics = this.sessionMetrics.get(callSid);
                        if (metrics) {
                            metrics.recoveryCount++;
                        }

                        // Mark session as inactive but don't end it - let recovery manager handle it
                        extConnectionData.isSessionActive = false;
                        extConnectionData.geminiSessionError = error.message;

                        sessionLogger.info(`🔄 [${callSid}] Gemini session error detected, session marked for recovery`);
                        // Recovery will be handled by the recovery manager's health checks or explicit recovery calls
                    },

                    onclose: (event: CloseEvent) => {
                        const sessionDuration = Date.now() - sessionStartTime;
                        sessionLogger.info(`🔌 [${callSid}] Gemini session closed after ${sessionDuration}ms`);
                        sessionLogger.info(`🔌 [${callSid}] Close event details:`, {
                            code: event?.code,
                            reason: event?.reason,
                            wasClean: event?.wasClean,
                            sessionDuration: sessionDuration
                        });

                        extConnectionData.isSessionActive = false;

                        // Check for early session termination (likely initialization failure)
                        if (sessionDuration < 5000) {
                            sessionLogger.error(
                                `❌ [${callSid}] Session closed too quickly (${sessionDuration}ms) - likely initialization failure`
                            );
                            sessionLogger.error(`❌ [${callSid}] This suggests API key, model, or configuration issues`);
                        }

                        // Session closed - cleanup handled by lifecycle manager

                        // Check if this is an unexpected close (connection still active)
                        // Check if this is an unexpected close (connection still active)
                        const isUnexpectedClose = extConnectionData.ws && extConnectionData.ws.readyState === 1;

                        if (isUnexpectedClose) {
                            sessionLogger.info(`⚠️ [${callSid}] Unexpected Gemini session close detected`);
                            this.contextManager.markSessionInterrupted(callSid, 'session_closed_unexpected');

                            // Session will be recovered by recovery manager's health checks or explicit recovery calls
                            sessionLogger.info(`🔄 [${callSid}] Session marked for recovery due to unexpected close`);
                        } else {
                            sessionLogger.info(`✅ [${callSid}] Gemini session closed normally (connection also closed)`);
                        }
                    },

                    onmessage: async (message: GeminiLiveMessage) => {
                        await routeGeminiMessage(callSid, message, extConnectionData, {
                            audioProcessor: this.audioProcessor,
                            activeConnections: this.activeConnections || undefined,
                            sessionMetrics: this.sessionMetrics,
                            forwardAudioFn: forwardAudio
                        });
                    }
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: config.voice
                            }
                        }
                    }
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192
            });

            // CRITICAL FIX: Send campaign script to Gemini as TEXT (not media)
            sessionLogger.info(`📝 [${callSid}] Session created - sending campaign script instructions to AI`);
            sessionLogger.info(`🔍 [${callSid}] DIAGNOSTIC: AI instructions validation:`, {
                hasInstructions: !!config.aiInstructions,
                instructionLength: config.aiInstructions?.length || 0,
                isLongEnough: (config.aiInstructions?.length || 0) > 100,
                scriptId: config.scriptId,
                scriptType: config.scriptType
            });
            
            if (config.aiInstructions && config.aiInstructions.length > 100) {
                sessionLogger.info(`📝 [${callSid}] Sending AI instructions (${config.aiInstructions.length} chars) to Gemini as TEXT`);
                
                // Send campaign script immediately - NO DELAYS
                try {
                    if (extConnectionData.geminiSession && config.aiInstructions) {
                        // **CRITICAL FIX**: Send campaign script as media with text/plain MIME type
                        // This follows CAMPAIGN_SCRIPT_POLICY.md - send full script as user message
                        await (extConnectionData.geminiSession as any).sendRealtimeInput({
                            media: {
                                data: Buffer.from(config.aiInstructions, 'utf-8').toString('base64'),
                                mimeType: 'text/plain'
                            }
                        });
                        sessionLogger.info(`✅ [${callSid}] Campaign script sent to Gemini successfully as text/plain media`);
                        
                        // Log confirmation that script was sent
                        const scriptPreview = config.aiInstructions.substring(0, 150);
                        sessionLogger.info(`📋 [${callSid}] Script preview sent: "${scriptPreview}..."`);
                        
                    } else {
                        sessionLogger.warn(`⚠️ [${callSid}] Session not ready for script sending - hasGeminiSession: ${!!extConnectionData.geminiSession}`);
                    }
                } catch (error) {
                    sessionLogger.error(`❌ [${callSid}] Failed to send campaign script to Gemini:`, error instanceof Error ? error : new Error(String(error)));
                    // This is critical - log the full error details
                    if (error instanceof Error) {
                        sessionLogger.error(`❌ [${callSid}] Script send error details:`, {
                            message: error.message,
                            stack: error.stack?.split('\n').slice(0, 3).join('\n')
                        });
                    }
                }
            } else {
                sessionLogger.error(`❌ [${callSid}] CRITICAL: No campaign script available or too short - Gemini will not receive instructions!`);
                sessionLogger.error(`❌ [${callSid}] Script details:`, {
                    hasInstructions: !!config.aiInstructions,
                    length: config.aiInstructions?.length || 0,
                    scriptId: config.scriptId,
                    campaignId: config.campaignId
                });
            }

            // Store the session in connectionData immediately after creation
            // This prevents race conditions where callbacks might execute before assignment
            extConnectionData.geminiSession = geminiSession as GeminiSession;

            // CRITICAL FIX: Ensure WebSocket is ready before marking session ready for audio
            const wsReady = !!(extConnectionData.ws || extConnectionData.twilioWs || extConnectionData.localWs);
            if (!wsReady) {
                sessionLogger.error(`❌ [${callSid}] CRITICAL: No WebSocket connection available when initializing audio forwarding`, {
                    sessionType: config.sessionType,
                    isTwilioCall: extConnectionData.isTwilioCall,
                    hasWs: !!extConnectionData.ws,
                    hasTwilioWs: !!extConnectionData.twilioWs,
                    hasLocalWs: !!extConnectionData.localWs
                });
            }

            // Mark session as ready for audio processing only after WebSocket validation
            extConnectionData.sessionReady = wsReady;
            extConnectionData.sessionInitialized = Date.now();

            // Initialize audio forwarding for this session only if WebSocket is ready
            if (wsReady) {
                initializeAudioForwarding(callSid, extConnectionData, config.sessionType || 'unknown');
                sessionLogger.info(`✅ [${callSid}] Audio forwarding initialized successfully`, {
                    sessionType: config.sessionType,
                    wsReady: wsReady
                });
            } else {
                sessionLogger.error(`❌ [${callSid}] Audio forwarding NOT initialized - WebSocket not ready`);
            }

            // Verify session was created successfully
            if (!geminiSession) {
                throw new Error('Gemini session creation returned null/undefined');
            }

            sessionLogger.info(`✅ [${callSid}] Gemini session created successfully and ready for audio`);

            // Add session health check after 5 seconds using timer manager
            timerManager.setTimeout(`${callSid}_health_check`, () => {
                if (extConnectionData.isSessionActive) {
                    sessionLogger.info(`✅ [${callSid}] Session health check: still active after 5 seconds`);
                } else {
                    sessionLogger.error(`❌ [${callSid}] Session health check: died within 5 seconds - check logs above for errors`);
                }
            }, 5000);

            return geminiSession as GeminiSession;

        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            const errorStack = error instanceof Error ? error.stack : undefined;
            sessionLogger.error(`❌ [${callSid}] Failed to create Gemini session:`, error instanceof Error ? error : new Error(String(error)));
            sessionLogger.error(`❌ [${callSid}] Error details:`, {
                message: errorMessage,
                code: error instanceof Error && 'code' in error ? (error as any).code : undefined,
                details: error instanceof Error && 'details' in error ? (error as any).details : undefined,
                stack: errorStack?.split('\n').slice(0, 5).join('\n') // First 5 lines of stack
            });

            // Log configuration that failed
            sessionLogger.error(`❌ [${callSid}] Failed configuration:`, {
                model: config.model,
                voice: config.voice,
                hasInstructions: !!config.aiInstructions,
                sessionType: config.sessionType,
                isIncomingCall: config.isIncomingCall
            });

            // Mark session as failed
            extConnectionData.isSessionActive = false;
            extConnectionData.geminiSessionError = errorMessage;

            return null;
        }
    }

    // Note: Message handling is now done directly in the onmessage callback (like old implementation)

    // REMOVED: sendInitialMessage method - now using Live API setup configuration
    // Initial instructions are sent via setup message in session creation, not as client content

    // Legacy method for backward compatibility - Live API handles continuous conversation automatically
    async sendTextToGemini(sessionId: string, geminiSession: GeminiSession, text: string): Promise<void> {
        sessionLogger.info(`⚠️ [${sessionId}] sendTextToGemini called but not needed in Live API - text: ${text.substring(0, 100)}...`);
        // In Live API, text should be sent as audio through sendRealtimeInput
        // This is a no-op for backward compatibility
    }

    // Legacy method for backward compatibility - Live API handles turn management automatically
    async sendTurnComplete(sessionId: string, geminiSession: GeminiSession): Promise<void> {
        sessionLogger.info(`⚠️ [${sessionId}] sendTurnComplete called but not needed in Live API`);
        // Voice Activity Detection (VAD) in the Live API automatically manages conversation turns
        // This is a no-op for backward compatibility
    }

    async sendAudioToGemini(callSid: string, geminiSession: GeminiSession, audioBuffer: Buffer): Promise<void> {
        await routeSendAudioToGemini(callSid, geminiSession, audioBuffer, {
            audioProcessor: this.audioProcessor,
            sessionMetrics: this.sessionMetrics,
            activeConnections: this.activeConnections,
            earlyAudioBuffers: this.earlyAudioBuffers
        });
    }

    // Send browser PCM audio to Gemini session (for local testing)
    async sendBrowserAudioToGemini(callSid: string, geminiSession: GeminiSession, base64Audio: string): Promise<void> {
        await routeSendBrowserAudioToGemini(callSid, geminiSession, base64Audio, {
            audioProcessor: this.audioProcessor,
            sessionMetrics: this.sessionMetrics,
            activeConnections: this.activeConnections,
            earlyAudioBuffers: this.earlyAudioBuffers
        });
    }

    // Recover session after interruption
    async recoverSession(callSid: string, reason: string): Promise<void> {
        await recoveryHandler(callSid, reason, this.contextManager, this.sessionMetrics, this.recoveryInProgress);
    }

    // Generate session summary
    async generateSummary(callSid: string, connectionData: ExtendedConnectionData, summaryPrompt: string): Promise<boolean> {
        try {
            sessionLogger.info(`📋 [${callSid}] Generating call summary`);
            
            if (!connectionData?.geminiSession) {
                sessionLogger.warn(`⚠️ [${callSid}] No Gemini session for summary generation`);
                return false;
            }

            connectionData.summaryRequested = true;
            connectionData.summaryText = '';

            // ALTERNATIVE APPROACH: For Live API, we can't easily get text-only summaries
            // Instead, we'll generate a summary from the conversation log we've been tracking
            // This is more reliable and doesn't interfere with the continuous conversation

            sessionLogger.info(`📝 [${callSid}] Generating summary from conversation log instead of requesting from AI`);

            // Generate summary from conversation log
            const conversationLog = connectionData.conversationLog || [];
            if (conversationLog.length > 0) {
                const summaryText = generateLocalSummary(conversationLog, summaryPrompt);
                connectionData.summaryText = summaryText;
                sessionLogger.info(`✅ [${callSid}] Local summary generated: ${summaryText.substring(0, 100)}...`);
            } else {
                connectionData.summaryText = 'No conversation content available for summary.';
                sessionLogger.info(`⚠️ [${callSid}] No conversation log available for summary`);
            }

            // Summary will be collected in the onmessage callback
            return true;

        } catch (error) {
            sessionLogger.error(`❌ [${callSid}] Error generating summary:`, error instanceof Error ? error : new Error(String(error)));
            return false;
        }
    }

    // Get session metrics
    getSessionMetrics(callSid: string): SessionMetrics | null {
        return this.sessionMetrics.get(callSid) || null;
    }

    // Clean up session
    async cleanupSession(callSid: string): Promise<void> {
        // Use a simple lock pattern to prevent concurrent cleanup
        const lockKey = `cleanup_${callSid}`;
        const cleanupInProgress = this.cleanupLocks.get(lockKey);
        
        if (cleanupInProgress) {
            // Cleanup already in progress, wait for it
            await cleanupInProgress;
            return;
        }
        
        // Create a promise to track this cleanup operation
        const cleanupPromise = this.performCleanup(callSid);
        this.cleanupLocks.set(lockKey, cleanupPromise);
        
        try {
            await cleanupPromise;
        } finally {
            this.cleanupLocks.delete(lockKey);
        }
    }
    
    private async performCleanup(callSid: string): Promise<void> {
        this.sessionMetrics.delete(callSid);
        this.recoveryInProgress.delete(callSid);
        // Clean up early audio buffers to prevent memory leaks
        this.earlyAudioBuffers.delete(callSid);
        // Clear all timers for this session
        timerManager.clearSessionTimers(callSid);
        sessionLogger.info(`🧹 [${callSid}] Session manager cleanup completed`);
    }

    /**
     * Set up a check to mark session as fully ready when Twilio connects
     * @param callSid - Call/session ID
     * @param connectionData - Connection data
     */
    private setupTwilioReadinessCheck(callSid: string, connectionData: ExtendedConnectionData): void {
        routingSetupTwilioReadinessCheck(callSid, connectionData, this.earlyAudioBuffers, this.activeConnections);
    }

    /**
     * Buffer early audio packets when session is not fully ready
     * @param callSid - Call/session ID
     * @param audioBuffer - Audio buffer to store
     */
    private bufferEarlyAudio(callSid: string, audioBuffer: Buffer): void {
        routingBufferEarlyAudio(callSid, audioBuffer, this.earlyAudioBuffers);
    }

    /**
     * Process buffered audio packets when session becomes ready
     * @param callSid - Call/session ID
     */
    private async processBufferedAudio(callSid: string): Promise<void> {
        await routingProcessBufferedAudio(callSid, {
            earlyAudioBuffers: this.earlyAudioBuffers,
            activeConnections: this.activeConnections,
            audioProcessor: this.audioProcessor,
            sessionMetrics: this.sessionMetrics
        });
    }

    /**
     * Get connection data for a session
     * @param callSid - Call/session ID
     * @returns Connection data or null
     */
    private getConnectionData(callSid: string): ExtendedConnectionData | null {
        return this.activeConnections?.get(callSid) as ExtendedConnectionData || null;
    }

    /**
     * Get audio settings
     */
    getAudioSettings() {
        return this.audioProcessor.getAudioSettings();
    }

    /**
     * Set audio settings
     */
    setAudioSettings(settings: any): boolean {
        this.audioProcessor.updateAudioSettings(settings);
        return true;
    }

    /**
     * Get audio quality monitor
     */
    getAudioQualityMonitor() {
        return 'audioQualityMonitor' in AudioProcessor ? (AudioProcessor as any).audioQualityMonitor : null;
    }

}
