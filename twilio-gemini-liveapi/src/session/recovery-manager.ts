// Session Recovery Manager

import { ConnectionData, GeminiClient } from '../types/global';
import { ContextManager, SessionContext } from './context-manager';
import { ConnectionHealthMonitor } from './health-monitor';
import { SessionManager } from './session-manager';
import { recoveryLockManager } from './recovery-lock';
import { recoveryLogger } from '../utils/logger';

interface RecoveryStatus {
    canRecover: boolean;
    isRecovering: boolean;
    recoveryAttempts: number;
    wasInterrupted: boolean;
    lastRecoveryTime?: number;
}

interface RecoveryQueueStatus {
    queueSize: number;
    inProgress: number;
    queuedSessions: string[];
    inProgressSessions: string[];
}

export class SessionRecoveryManager {
    private contextManager: ContextManager;
    private healthMonitor: ConnectionHealthMonitor;
    private geminiClient: GeminiClient;
    private sessionManager: SessionManager;
    private recoveryQueue: Map<string, any>;
    private recoveryInProgress: Set<string>;
    private maxRecoveryTime: number;
    private retryTimeouts: Map<string, NodeJS.Timeout>;
    private cleanupHandlers: Map<string, () => void>;
    private recoveryLocks?: Map<string, number>;
    private recoveryHealthChecks?: Map<string, NodeJS.Timeout>;

    constructor(contextManager: ContextManager, healthMonitor: ConnectionHealthMonitor, geminiClient: GeminiClient, sessionManager: SessionManager) {
        this.contextManager = contextManager;
        this.healthMonitor = healthMonitor;
        this.geminiClient = geminiClient;
        this.sessionManager = sessionManager;
        this.recoveryQueue = new Map(); // Queue of sessions awaiting recovery
        this.recoveryInProgress = new Set(); // Track sessions currently being recovered
        this.maxRecoveryTime = 30000; // 30 seconds max recovery time
        this.retryTimeouts = new Map(); // Track retry timeouts for cleanup
        this.cleanupHandlers = new Map(); // Track cleanup handlers for sessions
    }

    // Attempt to recover a session with enhanced context preservation and auto-retry
    async recoverSession(callSid: string, reason: string = 'unknown', activeConnections: Map<string, ConnectionData>): Promise<boolean> {
        // Use thread-safe lock manager to prevent concurrent recovery attempts
        const lockKey = `recovery_${callSid}`;
        const lockAcquired = await recoveryLockManager.acquireLock(lockKey);
        
        if (!lockAcquired) {
            recoveryLogger.info(`⏳ [${callSid}] Recovery already in progress, skipping duplicate attempt`);
            return false;
        }

        // Mark recovery as in progress with atomic operation
        this.recoveryInProgress.add(callSid);
        
        let recoveryAttempt = 0;

        try {
            const context = this.contextManager.getSessionContext(callSid);
            if (!context || !this.contextManager.canRecover(callSid)) {
                console.log(`❌ [${callSid}] Cannot recover session - no context or max attempts reached`);
                this.recoveryInProgress.delete(callSid);
                return false;
            }
            
            recoveryAttempt = this.contextManager.incrementRecoveryAttempt(callSid);

            console.log(`🔄 [${callSid}] Starting enhanced auto-recovery attempt ${recoveryAttempt} (reason: ${reason})`);
            console.log(`📊 [${callSid}] Context includes: ${context.conversationState.conversationLog.length} conversation entries, ${context.conversationState.fullTranscript.length} transcript entries`);
            // Get current connection data
            const connectionData = activeConnections.get(callSid);
            if (!connectionData) {
                console.log(`❌ [${callSid}] No active connection data found for recovery`);
                return false;
            }

            // Preserve current conversation state before recovery
            const currentConversationLog = (connectionData as any).conversationLog || [];
            const currentFullTranscript = (connectionData as any).fullTranscript || [];
            const currentSpeechTranscript = (connectionData as any).speechTranscript || [];

            // Mark context as being recovered with enhanced tracking
            context.recoveryInfo.lastRecoveryTime = Date.now();
            context.recoveryInfo.recoveryCount = recoveryAttempt;
            context.recoveryInfo.wasInterrupted = true;
            context.recoveryInfo.interruptionReason = reason;
            (context.recoveryInfo as any).autoRecoveryEnabled = true;

            // Update context with latest conversation data
            context.conversationState.conversationLog = currentConversationLog;
            context.conversationState.fullTranscript = currentFullTranscript;
            context.conversationState.speechTranscript = currentSpeechTranscript;

            // Save updated context
            this.contextManager.contextStore.set(callSid, context);

            // Attempt to recreate Gemini session with recovery context
            const recoveredSession = await this.recreateGeminiSessionWithRetry(callSid, context, connectionData, activeConnections);

            if (recoveredSession) {
                // Update connection data with recovered session
                connectionData.geminiSession = recoveredSession;
                (connectionData as any).isSessionActive = true;
                (connectionData as any).lastRecoveryTime = Date.now();

                // Preserve conversation data in connection
                (connectionData as any).conversationLog = currentConversationLog;
                (connectionData as any).fullTranscript = currentFullTranscript;
                (connectionData as any).speechTranscript = currentSpeechTranscript;

                // Send recovery notification to AI with full context
                await this.sendRecoveryNotification(callSid, recoveredSession, context);

                // Update health monitoring
                this.healthMonitor.trackConnection(callSid, 'recovered', {
                    attempt: recoveryAttempt,
                    reason,
                    conversationEntries: currentConversationLog.length,
                    transcriptEntries: currentFullTranscript.length,
                    autoRecovery: true
                });

                // Clear any pending retry timeout since recovery succeeded
                if (this.retryTimeouts.has(callSid)) {
                    clearTimeout(this.retryTimeouts.get(callSid)!);
                    this.retryTimeouts.delete(callSid);
                }

                // Schedule automatic health checks for this recovered session
                this.scheduleRecoveryHealthChecks(callSid, activeConnections);

                console.log(`✅ [${callSid}] Enhanced auto-recovery successful on attempt ${recoveryAttempt}`);
                console.log(`📊 [${callSid}] Preserved ${currentConversationLog.length} conversation entries and ${currentFullTranscript.length} transcript entries`);
                return true;
            } else {
                console.log(`❌ [${callSid}] Failed to recreate Gemini session during recovery`);

                // Schedule retry if we haven't exceeded max attempts
                if (this.contextManager.canRecover(callSid)) {
                    const retryDelay = this.calculateExponentialBackoff(recoveryAttempt);
                    console.log(`🔄 [${callSid}] Scheduling auto-retry in ${retryDelay}ms (attempt ${recoveryAttempt})`);

                    // Clear any existing retry timeout for this session
                    if (this.retryTimeouts.has(callSid)) {
                        clearTimeout(this.retryTimeouts.get(callSid)!);
                    }

                    // Schedule retry with exponential backoff
                    const timeoutId = setTimeout(() => {
                        this.retryTimeouts.delete(callSid);
                        // Use a fixed reason to prevent infinite growth
                        this.recoverSession(callSid, 'retry_attempt', activeConnections);
                    }, retryDelay);

                    this.retryTimeouts.set(callSid, timeoutId);
                }
                return false;
            }

        } catch (error: any) {
            console.error(`❌ [${callSid}] Error during enhanced session recovery:`, error);
            this.healthMonitor.trackConnection(callSid, 'failed', {
                error: error.message,
                recoveryAttempt,
                reason
            });

            // Schedule retry on error if possible
            if (this.contextManager.canRecover(callSid)) {
                const retryDelay = this.calculateExponentialBackoff(recoveryAttempt, true);
                console.log(`🔄 [${callSid}] Scheduling auto-retry after error in ${retryDelay}ms (attempt ${recoveryAttempt})`);
                const retryTimeout = setTimeout(() => {
                    this.retryTimeouts.delete(callSid);
                    // Use a fixed reason to prevent infinite growth
                    this.recoverSession(callSid, 'error_retry_attempt', activeConnections);
                }, retryDelay);
                this.retryTimeouts.set(callSid, retryTimeout);
            }
            return false;
        } finally {
            this.recoveryInProgress.delete(callSid);
            recoveryLockManager.releaseLock(lockKey);
        }
    }

    // Clean up all resources for a session
    cleanupSession(callSid: string): void {
        // Clear any pending retry timeouts
        if (this.retryTimeouts.has(callSid)) {
            clearTimeout(this.retryTimeouts.get(callSid)!);
            this.retryTimeouts.delete(callSid);
        }
        
        // Clear recovery locks
        const lockKey = `recovery_${callSid}`;
        recoveryLockManager.releaseLock(lockKey);
        
        // Remove from recovery tracking
        this.recoveryInProgress.delete(callSid);
        this.recoveryQueue.delete(callSid);
        
        console.log(`🧹 [${callSid}] Cleaned up recovery resources`);
    }

    // Recreate Gemini session with recovery context using the SessionManager
    // to ensure callbacks remain consistent with normal session creation
    private async recreateGeminiSession(callSid: string, context: SessionContext, connectionData: ConnectionData, activeConnections: Map<string, ConnectionData>): Promise<any | null> {
        try {
            const sessionConfig = context.sessionConfig;

            console.log(`🤖 [${callSid}] Recreating Gemini session with model: ${sessionConfig.model}, voice: ${sessionConfig.voice}`);
            console.log(`📝 [${callSid}] Using SessionManager for consistent session creation with campaign scripts`);

            // Use SessionManager for standardized session creation to ensure campaign scripts are included
            const newGeminiSession = await this.sessionManager.createGeminiSession(callSid, sessionConfig, connectionData);
            
            if (!newGeminiSession) {
                console.error(`❌ [${callSid}] Failed to recreate Gemini session via SessionManager`);
                return null;
            }

            // Track recovery success
            console.log(`✅ [${callSid}] Recovered Gemini session created successfully via SessionManager`);
            this.healthMonitor.trackConnection(callSid, 'connected', { recovered: true });

            return newGeminiSession;

        } catch (error: any) {
            console.error(`❌ [${callSid}] Error recreating Gemini session:`, error);
            this.healthMonitor.trackConnection(callSid, 'failed', {
                error: error.message,
                recovered: true
            });
            return null;
        }
    }

    // Send recovery notification to AI
    private async sendRecoveryNotification(callSid: string, geminiSession: any, context: SessionContext): Promise<void> {
        try {
            const recoveryMessage = this.contextManager.getRecoveryMessage(callSid);
            if (recoveryMessage && geminiSession) {
                console.log(`📢 [${callSid}] Sending recovery notification to AI`);

                // Send recovery notification using Live API
                // Check if session has the correct method available
                if (typeof geminiSession.sendRealtimeInput === 'function') {
                    await geminiSession.sendRealtimeInput({
                        clientContent: {
                            turns: [{
                                role: 'user',
                                parts: [{
                                    text: recoveryMessage
                                }]
                            }]
                        }
                    });
                } else {
                    console.log(`⚠️ [${callSid}] Gemini session doesn't support recovery notifications`);
                }
            }
        } catch (error) {
            console.error(`❌ [${callSid}] Error sending recovery notification:`, error);
        }
    }

    // Check if session needs recovery
    needsRecovery(callSid: string, activeConnections: Map<string, ConnectionData>): boolean {
        const context = this.contextManager.getSessionContext(callSid);
        const connectionData = activeConnections.get(callSid);

        return !!(context &&
                  connectionData &&
                  (!connectionData.geminiSession || !(connectionData as any).isSessionActive) &&
                  this.contextManager.canRecover(callSid));
    }

    // Get recovery status for a session
    getRecoveryStatus(callSid: string): RecoveryStatus {
        const context = this.contextManager.getSessionContext(callSid);
        const isRecovering = this.recoveryInProgress.has(callSid);

        return {
            canRecover: this.contextManager.canRecover(callSid),
            isRecovering,
            recoveryAttempts: context?.recoveryInfo?.recoveryCount || 0,
            wasInterrupted: context?.recoveryInfo?.wasInterrupted || false,
            lastRecoveryTime: context?.recoveryInfo?.lastRecoveryTime || undefined
        };
    }

    // Get recovery queue status
    getRecoveryQueueStatus(): RecoveryQueueStatus {
        return {
            queueSize: this.recoveryQueue.size,
            inProgress: this.recoveryInProgress.size,
            queuedSessions: Array.from(this.recoveryQueue.keys()),
            inProgressSessions: Array.from(this.recoveryInProgress)
        };
    }

    // Enhanced Gemini session recreation with retry logic
    private async recreateGeminiSessionWithRetry(callSid: string, context: SessionContext, connectionData: ConnectionData, activeConnections: Map<string, ConnectionData>, maxRetries: number = 3): Promise<any | null> {
        let lastError: Error | null = null;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`🔄 [${callSid}] Gemini session recreation attempt ${attempt}/${maxRetries}`);

                const session = await this.recreateGeminiSession(callSid, context, connectionData, activeConnections);
                if (session) {
                    console.log(`✅ [${callSid}] Gemini session recreated successfully on attempt ${attempt}`);
                    return session;
                }
            } catch (error: any) {
                lastError = error;
                console.warn(`⚠️ [${callSid}] Gemini session recreation attempt ${attempt} failed:`, error.message);

                if (attempt < maxRetries) {
                    const delay = attempt * 2000; // Exponential backoff: 2s, 4s, 6s
                    console.log(`⏳ [${callSid}] Waiting ${delay}ms before retry...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        console.error(`❌ [${callSid}] Failed to recreate Gemini session after ${maxRetries} attempts. Last error:`, lastError);
        return null;
    }

    // Schedule periodic health checks for recovered sessions
    private scheduleRecoveryHealthChecks(callSid: string, activeConnections: Map<string, ConnectionData>): void {
        // Clear any existing health check
        if (this.recoveryHealthChecks && this.recoveryHealthChecks.has(callSid)) {
            clearInterval(this.recoveryHealthChecks.get(callSid)!);
        }

        if (!this.recoveryHealthChecks) {
            this.recoveryHealthChecks = new Map();
        }

        // Schedule health check every 30 seconds for recovered sessions
        const healthCheckInterval = setInterval(async () => {
            const connectionData = activeConnections.get(callSid);
            if (!connectionData || !(connectionData as any).isSessionActive) {
                console.log(`🏥 [${callSid}] Session no longer active, stopping health checks`);
                clearInterval(healthCheckInterval);
                this.recoveryHealthChecks!.delete(callSid);
                return;
            }

            // Check if Gemini session is still healthy
            if (connectionData.geminiSession) {
                try {
                    // Simple health check - try to access session properties
                    const isHealthy = typeof connectionData.geminiSession.sendClientContent === 'function';
                    if (!isHealthy) {
                        console.log(`🚨 [${callSid}] Gemini session health check failed, triggering recovery`);
                        this.recoverSession(callSid, 'health_check_failed', activeConnections);
                    } else {
                        console.log(`💚 [${callSid}] Gemini session health check passed`);
                    }
                } catch (error: any) {
                    console.log(`🚨 [${callSid}] Gemini session health check error, triggering recovery:`, error.message);
                    this.recoverSession(callSid, 'health_check_error', activeConnections);
                }
            }
        }, 30000); // 30 seconds

        this.recoveryHealthChecks.set(callSid, healthCheckInterval);
        console.log(`🏥 [${callSid}] Recovery health checks scheduled`);
    }

    // Clean up recovery tracking for a session
    cleanupRecovery(callSid: string): void {
        this.recoveryQueue.delete(callSid);
        this.recoveryInProgress.delete(callSid);

        // Clean up retry timeouts
        if (this.retryTimeouts.has(callSid)) {
            clearTimeout(this.retryTimeouts.get(callSid)!);
            this.retryTimeouts.delete(callSid);
        }

        // Clean up health checks
        if (this.recoveryHealthChecks && this.recoveryHealthChecks.has(callSid)) {
            clearInterval(this.recoveryHealthChecks.get(callSid)!);
            this.recoveryHealthChecks.delete(callSid);
        }

        console.log(`🧹 [${callSid}] Recovery tracking and health checks cleaned up`);
    }

    // Clean up all recovery resources (for shutdown)
    cleanup(): void {
        const queueSize = this.recoveryQueue.size;
        const progressSize = this.recoveryInProgress.size;
        let healthCheckCount = 0;
        let retryTimeoutCount = 0;

        // Clear all health check intervals
        if (this.recoveryHealthChecks) {
            for (const [callSid, interval] of this.recoveryHealthChecks.entries()) {
                clearInterval(interval);
                healthCheckCount++;
            }
            this.recoveryHealthChecks.clear();
        }

        // Clear all retry timeouts to prevent memory leaks
        for (const [callSid, timeoutId] of this.retryTimeouts.entries()) {
            clearTimeout(timeoutId);
            retryTimeoutCount++;
        }
        this.retryTimeouts.clear();

        this.recoveryQueue.clear();
        this.recoveryInProgress.clear();

        console.log(`🧹 RecoveryManager: Cleared ${queueSize} queued, ${progressSize} in-progress recoveries, ${healthCheckCount} health check intervals, and ${retryTimeoutCount} retry timeouts`);
    }

    /**
     * Calculate exponential backoff delay for retry attempts
     * @param attempt - Current attempt number (1-based)
     * @param isError - Whether this is an error retry (shorter delays)
     * @returns Delay in milliseconds
     */
    private calculateExponentialBackoff(attempt: number, isError: boolean = false): number {
        const baseDelay = isError ? 1000 : 2000; // 1s for errors, 2s for normal retries
        const maxDelay = isError ? 8000 : 30000; // 8s max for errors, 30s max for normal
        const jitter = Math.random() * 0.1; // Add 10% jitter to prevent thundering herd

        const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
        const jitteredDelay = delay * (1 + jitter);

        return Math.round(jitteredDelay);
    }

    /**
     * Get recovery metrics for monitoring
     * @returns Recovery metrics object
     */
    getRecoveryMetrics(): {
        totalRecoveries: number;
        successfulRecoveries: number;
        failedRecoveries: number;
        averageRecoveryTime: number;
        activeRecoveries: number;
        queuedRecoveries: number;
    } {
        // This would be enhanced with actual metrics tracking
        return {
            totalRecoveries: this.recoveryQueue.size + this.recoveryInProgress.size,
            successfulRecoveries: 0, // Would track this with actual metrics
            failedRecoveries: 0, // Would track this with actual metrics
            averageRecoveryTime: 0, // Would calculate from tracked recovery times
            activeRecoveries: this.recoveryInProgress.size,
            queuedRecoveries: this.recoveryQueue.size
        };
    }
}

// Export alias for backward compatibility
export { SessionRecoveryManager as RecoveryManager };