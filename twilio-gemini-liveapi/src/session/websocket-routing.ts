import { AudioProcessor } from '../audio/audio-processor';
import { forwardAudio } from '../audio/audio-forwarding';
import { sessionLogger } from '../utils/logger';
import { sendAudioBufferToGemini } from '../audio/gemini-sender';
import { ConnectionData, GeminiSession } from '../types/global';
import { SessionMetrics } from './metrics';

interface RoutingDeps {
    audioProcessor: AudioProcessor;
    sessionMetrics: Map<string, SessionMetrics>;
    activeConnections?: Map<string, ConnectionData> | null;
    earlyAudioBuffers: Map<string, Buffer[]>;
}

export async function sendAudioToGemini(
    callSid: string,
    geminiSession: GeminiSession | null,
    audioBuffer: Buffer,
    deps: RoutingDeps
): Promise<void> {
    const { audioProcessor, sessionMetrics, activeConnections, earlyAudioBuffers } = deps;
    try {
        sessionLogger.info(`🔍 [${callSid}] sendAudioToGemini called - geminiSession: ${!!geminiSession}, audioBuffer: ${!!audioBuffer}, audioSize: ${audioBuffer?.length || 0}`);
        if (!geminiSession || !audioBuffer) {
            sessionLogger.info(`⚠️ [${callSid}] sendAudioToGemini early return - missing geminiSession or audioBuffer`);
            return;
        }
        const connectionData = activeConnections?.get(callSid) as any;
        if (!connectionData?.sessionReady) {
            sessionLogger.info(`⚠️ [${callSid}] Session not ready for audio processing yet`);
            return;
        }
        if (!connectionData.fullyReady) {
            sessionLogger.warn(`⚠️ [${callSid}] Session not fully ready, buffering audio packet`);
            bufferEarlyAudio(callSid, audioBuffer, earlyAudioBuffers);
            return;
        }
        if (connectionData.geminiSessionError) {
            sessionLogger.error(`❌ [${callSid}] Session has error, skipping audio: ${connectionData.geminiSessionError}`);
            return;
        }
        const metrics = sessionMetrics.get(callSid);
        if (metrics) {
            metrics.messagesSent = (metrics.messagesSent || 0) + 1;
            metrics.lastActivity = Date.now();
        }
        const pcmBuffer = audioProcessor.convertUlawToPCM(audioBuffer, false);
        if (pcmBuffer.length === 0) {
            sessionLogger.warn(`⚠️ [${callSid}] PCM conversion resulted in empty buffer - skipping audio send`);
            return;
        }
        await sendAudioBufferToGemini(callSid, connectionData, pcmBuffer);
    } catch (error) {
        sessionLogger.error(`❌ [${callSid}] Error sending audio to Gemini:`, error instanceof Error ? error : new Error(String(error)));
    }
}

export async function sendBrowserAudioToGemini(
    callSid: string,
    geminiSession: GeminiSession | null,
    base64Audio: string,
    deps: RoutingDeps
): Promise<void> {
    const { sessionMetrics, activeConnections, audioProcessor } = deps;
    try {
        sessionLogger.info(`🔍 [${callSid}] sendBrowserAudioToGemini called - geminiSession: ${!!geminiSession}, audioSize: ${base64Audio?.length || 0}`);
        if (!geminiSession || !base64Audio) {
            sessionLogger.info(`⚠️ [${callSid}] sendBrowserAudioToGemini early return - missing geminiSession or audio`);
            return;
        }
        const connectionData = activeConnections?.get(callSid) as any;
        if (!connectionData?.sessionReady) {
            sessionLogger.info(`⚠️ [${callSid}] Session not ready for audio processing yet`);
            return;
        }
        const metrics = sessionMetrics.get(callSid);
        if (metrics) {
            metrics.messagesSent = (metrics.messagesSent || 0) + 1;
            metrics.lastActivity = Date.now();
        }
        const pcmBuffer = Buffer.from(base64Audio, 'base64');
        await sendAudioBufferToGemini(callSid, connectionData, pcmBuffer);
    } catch (error) {
        sessionLogger.error(`❌ [${callSid}] Error sending browser audio to Gemini:`, error instanceof Error ? error : new Error(String(error)));
    }
}

export function setupTwilioReadinessCheck(callSid: string, connectionData: any, earlyAudioBuffers: Map<string, Buffer[]>, activeConnections?: Map<string, ConnectionData> | null): void {
    // Mark as ready immediately if both conditions are met
    if (connectionData.twilioConnected && connectionData.isSessionActive) {
        connectionData.fullyReady = true;
        sessionLogger.info(`🎯 [${callSid}] Session now fully ready - Twilio connection established`);
        
        // Send initial greeting for inbound calls immediately
        if (connectionData.isIncomingCall && connectionData.geminiSession) {
            sendInitialGreeting(callSid, connectionData).catch(error => {
                sessionLogger.error(`❌ [${callSid}] Error sending initial greeting:`, error);
            });
        }
        
        processBufferedAudio(callSid, { earlyAudioBuffers, activeConnections }, connectionData.geminiSession);
        return;
    }
    
    // If not ready immediately, set up event-based readiness (no polling)
    const checkReadiness = async () => {
        if (connectionData.twilioConnected && connectionData.isSessionActive && !connectionData.fullyReady) {
            connectionData.fullyReady = true;
            sessionLogger.info(`🎯 [${callSid}] Session now fully ready - Twilio connection established`);
            
            if (connectionData.isIncomingCall && connectionData.geminiSession) {
                await sendInitialGreeting(callSid, connectionData);
            }
            
            processBufferedAudio(callSid, { earlyAudioBuffers, activeConnections }, connectionData.geminiSession);
        }
    };
    
    // Use immediate check with minimal fallback timeout
    process.nextTick(checkReadiness);
    
    // Minimal safety timeout (reduced from 10 seconds to 2 seconds)
    setTimeout(async () => {
        if (!connectionData.fullyReady && connectionData.isSessionActive) {
            sessionLogger.info(`🎯 [${callSid}] Proceeding with session - marking as ready`);
            connectionData.fullyReady = true;
            
            if (connectionData.isIncomingCall && connectionData.geminiSession) {
                await sendInitialGreeting(callSid, connectionData);
            }
            
            processBufferedAudio(callSid, { earlyAudioBuffers, activeConnections }, connectionData.geminiSession);
        }
    }, 2000); // Reduced from 10 seconds to 2 seconds
}

async function sendInitialGreeting(callSid: string, connectionData: any): Promise<void> {
    try {
        // Only send greeting if no audio has been received yet
        if (connectionData.hasReceivedAudio) {
            sessionLogger.debug(`[${callSid}] Skipping greeting - audio already received`);
            return;
        }
        
        sessionLogger.info(`👋 [${callSid}] Sending initial greeting for inbound call`);
        
        // Send a text prompt to trigger the AI to speak first
        const greetingPrompt = "Please greet the caller warmly and ask how you can help them today.";
        
        // Use sendRealtimeInput with text encoded as base64
        await connectionData.geminiSession.sendRealtimeInput({
            media: {
                data: Buffer.from(greetingPrompt, 'utf-8').toString('base64'),
                mimeType: 'text/plain'
            }
        });
        
        sessionLogger.info(`✅ [${callSid}] Initial greeting prompt sent successfully`);
    } catch (error) {
        sessionLogger.error(`❌ [${callSid}] Error sending initial greeting:`, error instanceof Error ? error : new Error(String(error)));
    }
}

export function bufferEarlyAudio(callSid: string, audioBuffer: Buffer, earlyAudioBuffers: Map<string, Buffer[]>): void {
    if (!earlyAudioBuffers.has(callSid)) {
        earlyAudioBuffers.set(callSid, []);
    }
    const buffers = earlyAudioBuffers.get(callSid)!;
    buffers.push(audioBuffer);
    if (buffers.length > 50) {
        buffers.shift();
    }
    sessionLogger.debug(`📦 [${callSid}] Buffered audio packet (${buffers.length} total)`);
}

export async function processBufferedAudio(
    callSid: string,
    deps: { earlyAudioBuffers: Map<string, Buffer[]>; activeConnections?: Map<string, ConnectionData> | null; audioProcessor?: AudioProcessor; sessionMetrics?: Map<string, SessionMetrics>; },
    geminiSession?: GeminiSession | null
): Promise<void> {
    const { earlyAudioBuffers, activeConnections, audioProcessor, sessionMetrics } = deps;
    const buffers = earlyAudioBuffers.get(callSid);
    if (!buffers || buffers.length === 0) {
        return;
    }
    sessionLogger.info(`🔄 [${callSid}] Processing ${buffers.length} buffered audio packets`);
    const connectionData = activeConnections?.get(callSid) as any;
    const session = geminiSession || connectionData?.geminiSession;
    if (!session) {
        sessionLogger.warn(`⚠️ [${callSid}] No Gemini session for buffered audio processing`);
        earlyAudioBuffers.delete(callSid);
        return;
    }
    for (const buffer of buffers) {
        try {
            await sendAudioToGemini(callSid, session, buffer, {
                audioProcessor: audioProcessor!,
                sessionMetrics: sessionMetrics!,
                activeConnections,
                earlyAudioBuffers
            });
        } catch (error) {
            sessionLogger.error(`❌ [${callSid}] Error processing buffered audio:`, error instanceof Error ? error : new Error(String(error)));
            break;
        }
    }
    earlyAudioBuffers.delete(callSid);
    sessionLogger.info(`✅ [${callSid}] Finished processing buffered audio packets`);
}
