// Comprehensive Configuration System for Twilio Gemini Project
// Centralizes all configuration with environment variable support and validation

import dotenv from '../utils/dotenv-stub';
import { configLogger as logger } from '../utils/logger';
import type { AppConfig } from '../types/global';
import { ConfigValidator } from './validator';
import { environment } from './sections/environment';
import { server } from './sections/server';
import { auth } from './sections/auth';
import { twilio } from './sections/twilio';
import { ai } from './sections/ai';
import { audio } from './sections/audio';
import { websocket } from './sections/websocket';
import { transcription } from './sections/transcription';
import { campaigns } from './sections/campaigns';
import { localization } from './sections/localization';
import { voices } from './sections/voices';
import { business } from './sections/business';
import { prompts } from './sections/prompts';
import { security } from './sections/security';
import { performance } from './sections/performance';
import { timeouts } from './sections/timeouts';
import { limits } from './sections/limits';

dotenv.config();

if (process.env.NODE_ENV === 'test') {
    process.env.GEMINI_API_KEY ??= 'test-api-key';
    process.env.TWILIO_ACCOUNT_SID ??= 'ACXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX';
    process.env.TWILIO_AUTH_TOKEN ??= 'test-auth-token';
    process.env.PUBLIC_URL ??= 'http://localhost:3101';
}

export const config: AppConfig = {
    environment,
    server,
    auth,
    twilio,
    ai,
    audio,
    websocket,
    transcription,
    campaigns,
    localization,
    voices,
    business,
    prompts,
    security,
    performance,
    timeouts,
    limits
};

export function validateConfig(): boolean {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
        try {
            ConfigValidator.validateRequired(config.auth.gemini.apiKey, 'GEMINI_API_KEY');
        } catch (e) { errors.push(e instanceof Error ? e.message : String(e)); }

        try {
            ConfigValidator.validateRequired(config.auth.twilio.accountSid, 'TWILIO_ACCOUNT_SID');
        } catch (e) { errors.push(e instanceof Error ? e.message : String(e)); }

        try {
            ConfigValidator.validateRequired(config.auth.twilio.authToken, 'TWILIO_AUTH_TOKEN');
        } catch (e) { errors.push(e instanceof Error ? e.message : String(e)); }

        try {
            ConfigValidator.validateUrl(config.server.publicUrl, 'PUBLIC_URL', true);
        } catch (e) { errors.push(e instanceof Error ? e.message : String(e)); }

        if (!config.auth.deepgram.apiKey) {
            warnings.push('DEEPGRAM_API_KEY not set - transcription features will be limited');
        }

        if (!config.auth.openai.apiKey) {
            warnings.push('OPENAI_API_KEY not set - OpenAI features will be unavailable');
        }

        if (config.server.port < 1 || config.server.port > 65535) {
            errors.push(`Invalid PORT: ${config.server.port} (must be 1-65535)`);
        }

        if (config.ai.gemini.temperature < 0 || config.ai.gemini.temperature > 2) {
            errors.push(`Invalid GEMINI_TEMPERATURE: ${config.ai.gemini.temperature} (must be 0-2)`);
        }

        if (config.ai.gemini.topP < 0 || config.ai.gemini.topP > 1) {
            errors.push(`Invalid GEMINI_TOP_P: ${config.ai.gemini.topP} (must be 0-1)`);
        }

        if (config.audio.sampleRate < 8000 || config.audio.sampleRate > 48000) {
            warnings.push(`Unusual SAMPLE_RATE: ${config.audio.sampleRate} (recommended: 8000-48000)`);
        }

        const supportedLangs = config.localization.supportedLanguages;
        if (!supportedLangs.includes(config.localization.defaultLanguage)) {
            errors.push(`DEFAULT_LANGUAGE '${config.localization.defaultLanguage}' not in SUPPORTED_LANGUAGES`);
        }

        if (config.business.validation.maxVehicles < 1 || config.business.validation.maxVehicles > 50) {
            warnings.push(`Unusual MAX_VEHICLES: ${config.business.validation.maxVehicles} (recommended: 1-50)`);
        }

        if (config.websocket.heartbeatInterval < 1000 || config.websocket.heartbeatInterval > 120000) {
            errors.push(`Invalid HEARTBEAT_INTERVAL: ${config.websocket.heartbeatInterval} (must be 1000-120000)`);
        }

        if (config.websocket.heartbeatTimeout < 1000 || config.websocket.heartbeatTimeout > 60000) {
            errors.push(`Invalid HEARTBEAT_TIMEOUT: ${config.websocket.heartbeatTimeout} (must be 1000-60000)`);
        }

        if (errors.length > 0) {
            logger.error('Configuration validation failed', {});
            errors.forEach(error => logger.error(error));
            throw new Error(`Configuration validation failed with ${errors.length} error(s)`);
        }

        if (warnings.length > 0) {
            logger.warn('Configuration warnings', {});
            warnings.forEach(warning => logger.warn(warning));
        }

        logger.info('Configuration validation passed');
        if (warnings.length > 0) {
            logger.info(`(${warnings.length} warning(s) - see above)`);
        }

        return true;
    } catch (error) {
        if (errors.length === 0) {
            logger.error('Configuration validation failed', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
        throw error;
    }
}

export function getConfigValue(path: string, fallback: any = null): any {
    const keys = path.split('.');
    let value: any = config;

    for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
            value = value[key];
        } else {
            return fallback;
        }
    }

    return value;
}

export function getSafeConfig(): any {
    const safeConfig = JSON.parse(JSON.stringify(config));
    if (safeConfig.auth) {
        Object.keys(safeConfig.auth).forEach(provider => {
            if (safeConfig.auth[provider].apiKey) {
                safeConfig.auth[provider].apiKey = '***REDACTED***';
            }
            if (safeConfig.auth[provider].authToken) {
                safeConfig.auth[provider].authToken = '***REDACTED***';
            }
        });
    }
    return safeConfig;
}

export function getConfigSummary(): any {
    return {
        environment: config.environment.nodeEnv,
        server: {
            port: config.server.port,
            publicUrl: config.server.publicUrl ? 'SET' : 'NOT SET'
        },
        auth: {
            gemini: config.auth.gemini.apiKey ? 'SET' : 'NOT SET',
            twilio: config.auth.twilio.accountSid ? 'SET' : 'NOT SET',
            openai: config.auth.openai.apiKey ? 'SET' : 'NOT SET',
            deepgram: config.auth.deepgram.apiKey ? 'SET' : 'NOT SET'
        },
        ai: {
            defaultModel: config.ai.gemini.defaultModel,
            defaultVoice: config.ai.gemini.defaultVoice,
            availableModels: config.ai.gemini.availableModels.length
        },
        campaigns: {
            totalCampaigns: config.campaigns.totalCampaigns,
            scriptsPath: config.campaigns.scriptsPath ? 'SET' : 'NOT SET',
            enableCustomScripts: config.campaigns.enableCustomScripts
        },
        localization: {
            defaultLanguage: config.localization.defaultLanguage,
            supportedLanguages: config.localization.supportedLanguages,
            enableMultiLanguage: config.localization.enableMultiLanguage
        },
        performance: {
            enableCaching: config.performance.enableCaching,
            maxConcurrentCalls: config.performance.maxConcurrentCalls,
            enableMetrics: config.performance.enableMetrics
        }
    };
}

export function validateConfigSection(section: string): { valid: boolean; error?: string } {
    const validators: Record<string, () => void> = {
        auth: () => {
            ConfigValidator.validateRequired(config.auth.gemini.apiKey, 'GEMINI_API_KEY');
            ConfigValidator.validateRequired(config.auth.twilio.accountSid, 'TWILIO_ACCOUNT_SID');
            ConfigValidator.validateRequired(config.auth.twilio.authToken, 'TWILIO_AUTH_TOKEN');
        },
        server: () => {
            ConfigValidator.validateUrl(config.server.publicUrl, 'PUBLIC_URL', true);
            ConfigValidator.validatePort(String(config.server.port), 'PORT');
        },
        ai: () => {
            ConfigValidator.validateNumber(String(config.ai.gemini.temperature), 'GEMINI_TEMPERATURE', 0, 2);
            ConfigValidator.validateNumber(String(config.ai.gemini.topP), 'GEMINI_TOP_P', 0, 1);
            ConfigValidator.validateNumber(String(config.ai.gemini.topK), 'GEMINI_TOP_K', 1, 100);
        },
        audio: () => {
            ConfigValidator.validateNumber(String(config.audio.sampleRate), 'SAMPLE_RATE', 8000, 48000);
            ConfigValidator.validateNumber(String(config.audio.twilioSampleRate), 'TWILIO_SAMPLE_RATE', 8000, 48000);
        },
        websocket: () => {
            ConfigValidator.validateNumber(String(config.websocket.heartbeatInterval), 'HEARTBEAT_INTERVAL', 1000, 120000);
            ConfigValidator.validateNumber(String(config.websocket.heartbeatTimeout), 'HEARTBEAT_TIMEOUT', 1000, 60000);
        }
    };

    if (validators[section]) {
        try {
            validators[section]();
            return { valid: true };
        } catch (error) {
            return { valid: false, error: error instanceof Error ? error.message : String(error) };
        }
    }

    return { valid: false, error: `Unknown configuration section: ${section}` };
}

export default config;
