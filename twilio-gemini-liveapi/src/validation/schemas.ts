import { z } from 'zod';

// Common validation schemas
export const phoneNumberSchema = z.string()
  .regex(/^\+[1-9]\d{1,14}$/, 'Invalid phone number format. Must be in E.164 format (e.g., +1234567890)');

export const callSidSchema = z.string()
  .regex(/^CA[a-f0-9]{32}$/, 'Invalid CallSid format');

export const sessionIdSchema = z.string()
  .min(1, 'Session ID cannot be empty')
  .max(100, 'Session ID too long');

// Voice options validation
export const voiceSchema = z.enum([
  'Aoede', '<PERSON>ron', '<PERSON>rir', '<PERSON>re', 'Puck'
], {
  errorMap: () => ({ message: 'Invalid voice. Must be one of: Aoede, Charon, Fenrir, Kore, Puck' })
});

// Model options validation
export const modelSchema = z.enum([
  'gemini-2.0-flash-exp', 'gemini-1.5-flash', 'gemini-1.5-pro'
], {
  errorMap: () => ({ message: 'Invalid model. Must be one of: gemini-2.0-flash-exp, gemini-1.5-flash, gemini-1.5-pro' })
});

// Language validation
export const languageSchema = z.enum([
  'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'
], {
  errorMap: () => ({ message: 'Invalid language code' })
});

// Session configuration schemas
export const sessionConfigSchema = z.object({
  voice: voiceSchema.optional(),
  model: modelSchema.optional(),
  aiInstructions: z.string().max(50000, 'AI instructions too long').optional(),
  task: z.string().max(50000, 'Task description too long').optional(), // Legacy field
  targetName: z.string().max(100, 'Target name too long').optional(),
  targetPhoneNumber: phoneNumberSchema.optional(),
  outputLanguage: languageSchema.optional(),
}).refine(data => {
  // Ensure at least one of aiInstructions or task is provided if either is present
  if (data.aiInstructions && data.task) {
    return false; // Don't allow both
  }
  return true;
}, {
  message: 'Provide either aiInstructions or task, not both'
});

// WebSocket message schemas
export const webSocketMessageSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('audio'),
    data: z.string(), // Base64 encoded audio
    callSid: callSidSchema
  }),
  z.object({
    type: z.literal('text'),
    data: z.string().max(1000, 'Text message too long'),
    callSid: callSidSchema
  }),
  z.object({
    type: z.literal('control'),
    action: z.enum(['start', 'stop', 'pause', 'resume']),
    callSid: callSidSchema
  }),
  z.object({
    type: z.literal('config'),
    config: sessionConfigSchema,
    callSid: callSidSchema
  })
]);

// API endpoint schemas
export const makeCallSchema = z.object({
  to: phoneNumberSchema,
  from: phoneNumberSchema,
  aiInstructions: z.string().min(1, 'AI instructions required').max(50000, 'AI instructions too long'),
  voice: voiceSchema,
  model: modelSchema,
  targetName: z.string().max(100, 'Target name too long').optional(),
  outputLanguage: languageSchema.optional(),
});

export const updateSystemMessageSchema = z.object({
  systemMessage: z.string().max(10000, 'System message too long'),
  callSid: callSidSchema.optional(),
  sessionId: sessionIdSchema.optional(),
}).refine(data => data.callSid || data.sessionId, {
  message: 'Either callSid or sessionId must be provided'
});

export const saveConfigSchema = z.object({
  name: z.string().min(1, 'Config name required').max(100, 'Config name too long'),
  config: sessionConfigSchema,
});

// Twilio webhook schemas
export const twilioWebhookSchema = z.object({
  CallSid: callSidSchema,
  From: phoneNumberSchema,
  To: phoneNumberSchema,
  CallStatus: z.enum(['ringing', 'in-progress', 'completed', 'busy', 'failed', 'no-answer', 'canceled']),
  Direction: z.enum(['inbound', 'outbound']),
  AccountSid: z.string().regex(/^AC[a-f0-9]{32}$/, 'Invalid AccountSid format').optional(),
});

// Audio data validation
export const audioDataSchema = z.object({
  data: z.instanceof(Buffer).or(z.string()), // Buffer or base64 string
  format: z.enum(['mulaw', 'pcm', 'opus']).optional(),
  sampleRate: z.number().int().min(8000).max(48000).optional(),
  channels: z.number().int().min(1).max(2).optional(),
});

// Campaign script validation
export const campaignScriptSchema = z.object({
  agentPersona: z.object({
    name: z.string().max(100),
    role: z.string().max(100),
    personality: z.string().max(500),
    communicationStyle: z.string().max(500),
  }),
  campaign: z.object({
    objective: z.string().max(500),
    targetAudience: z.string().max(500),
    keyMessages: z.array(z.string().max(200)).max(10),
    callToAction: z.string().max(200),
  }),
  conversationFlow: z.object({
    opening: z.string().max(1000),
    mainPoints: z.array(z.string().max(500)).max(10),
    objectionHandling: z.array(z.object({
      objection: z.string().max(200),
      response: z.string().max(500),
    })).max(10),
    closing: z.string().max(1000),
  }),
  complianceGuidelines: z.array(z.string().max(300)).max(10),
});

// Export type inference helpers
export type SessionConfig = z.infer<typeof sessionConfigSchema>;
export type WebSocketMessage = z.infer<typeof webSocketMessageSchema>;
export type MakeCallRequest = z.infer<typeof makeCallSchema>;
export type UpdateSystemMessageRequest = z.infer<typeof updateSystemMessageSchema>;
export type SaveConfigRequest = z.infer<typeof saveConfigSchema>;
export type TwilioWebhook = z.infer<typeof twilioWebhookSchema>;
export type AudioData = z.infer<typeof audioDataSchema>;
export type CampaignScript = z.infer<typeof campaignScriptSchema>;
