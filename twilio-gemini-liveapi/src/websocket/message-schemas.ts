import { z } from 'zod';

// ========================================
// TWILIO MESSAGE SCHEMAS
// ========================================

/**
 * Twilio Media Stream - Start message schema
 */
export const TwilioStartMessageSchema = z.object({
    event: z.literal('start'),
    start: z.object({
        streamSid: z.string().min(1),
        accountSid: z.string().regex(/^AC[a-f0-9]{32}$/),
        callSid: z.string().regex(/^CA[a-f0-9]{32}$/),
        tracks: z.array(z.string()).optional(),
        customParameters: z.record(z.any()).optional()
    }),
    streamSid: z.string().optional()
});

/**
 * Twilio Media Stream - Media message schema
 */
export const TwilioMediaMessageSchema = z.object({
    event: z.literal('media'),
    media: z.object({
        payload: z.string().min(1), // Base64 encoded audio
        chunk: z.string().optional(),
        timestamp: z.string().optional(),
        track: z.string().optional()
    }),
    streamSid: z.string().optional(),
    sequenceNumber: z.string().optional()
});

/**
 * Twilio Media Stream - Stop message schema
 */
export const TwilioStopMessageSchema = z.object({
    event: z.literal('stop'),
    streamSid: z.string().optional(),
    stop: z.object({
        accountSid: z.string().optional(),
        callSid: z.string().optional()
    }).optional()
});

/**
 * Twilio Media Stream - Mark message schema
 */
export const TwilioMarkMessageSchema = z.object({
    event: z.literal('mark'),
    streamSid: z.string().optional(),
    mark: z.object({
        name: z.string()
    }).optional()
});

/**
 * Twilio Media Stream - Connected message schema
 */
export const TwilioConnectedMessageSchema = z.object({
    event: z.literal('connected'),
    protocol: z.string().optional(),
    version: z.string().optional()
});

/**
 * Union of all Twilio message types
 */
export const TwilioMessageSchema = z.discriminatedUnion('event', [
    TwilioStartMessageSchema,
    TwilioMediaMessageSchema,
    TwilioStopMessageSchema,
    TwilioMarkMessageSchema,
    TwilioConnectedMessageSchema
]);

// ========================================
// LOCAL BROWSER MESSAGE SCHEMAS
// ========================================

/**
 * Local browser - Start session message schema
 */
export const LocalStartMessageSchema = z.object({
    type: z.literal('start-session'),
    aiInstructions: z.string().optional(),
    voice: z.string().optional(),
    model: z.string().optional(),
    scriptId: z.string().optional()
});

/**
 * Local browser - Audio data message schema
 */
export const LocalAudioMessageSchema = z.object({
    type: z.enum(['audio-data', 'audio']),
    audio: z.string().optional(), // Base64 encoded audio
    audioData: z.string().optional() // Alternative field name
});

/**
 * Local browser - Text message schema
 */
export const LocalTextMessageSchema = z.object({
    type: z.literal('text-message'),
    text: z.string().min(1)
});

/**
 * Local browser - End session message schema
 */
export const LocalEndSessionMessageSchema = z.object({
    type: z.literal('end-session')
});

/**
 * Local browser - Heartbeat message schema
 */
export const LocalHeartbeatMessageSchema = z.object({
    type: z.literal('heartbeat')
});

/**
 * Local browser - Turn complete message schema
 */
export const LocalTurnCompleteMessageSchema = z.object({
    type: z.literal('turn-complete')
});

/**
 * Local browser - Request summary message schema
 */
export const LocalRequestSummaryMessageSchema = z.object({
    type: z.literal('request-summary')
});

/**
 * Local browser - Audio response message schema (from server)
 */
export const LocalAudioResponseMessageSchema = z.object({
    type: z.literal('audio-response'),
    audio: z.string().optional()
});

/**
 * Union of all local browser message types
 */
export const LocalMessageSchema = z.discriminatedUnion('type', [
    LocalStartMessageSchema,
    LocalAudioMessageSchema,
    LocalTextMessageSchema,
    LocalEndSessionMessageSchema,
    LocalHeartbeatMessageSchema,
    LocalTurnCompleteMessageSchema,
    LocalRequestSummaryMessageSchema,
    LocalAudioResponseMessageSchema
]);

// ========================================
// SESSION CONFIGURATION SCHEMAS
// ========================================

/**
 * Session configuration schema
 */
export const SessionConfigSchema = z.object({
    aiInstructions: z.string().min(1),
    voice: z.string().min(1),
    model: z.string().min(1),
    targetName: z.string().nullable().optional(),
    targetPhoneNumber: z.string().nullable().optional(),
    scriptType: z.string().min(1),
    scriptId: z.string().min(1),
    isIncomingCall: z.boolean().optional(),
    isTestMode: z.boolean().optional()
});

// ========================================
// VALIDATION HELPERS
// ========================================

/**
 * Validates a Twilio message and returns parsed data or error
 */
export function validateTwilioMessage(data: unknown): {
    success: boolean;
    data?: z.infer<typeof TwilioMessageSchema>;
    error?: z.ZodError;
} {
    const result = TwilioMessageSchema.safeParse(data);
    return {
        success: result.success,
        data: result.success ? result.data : undefined,
        error: result.success ? undefined : result.error
    };
}

/**
 * Validates a local browser message and returns parsed data or error
 */
export function validateLocalMessage(data: unknown): {
    success: boolean;
    data?: z.infer<typeof LocalMessageSchema>;
    error?: z.ZodError;
} {
    const result = LocalMessageSchema.safeParse(data);
    return {
        success: result.success,
        data: result.success ? result.data : undefined,
        error: result.success ? undefined : result.error
    };
}

/**
 * Validates session configuration and returns parsed data or error
 */
export function validateSessionConfig(data: unknown): {
    success: boolean;
    data?: z.infer<typeof SessionConfigSchema>;
    error?: z.ZodError;
} {
    const result = SessionConfigSchema.safeParse(data);
    return {
        success: result.success,
        data: result.success ? result.data : undefined,
        error: result.success ? undefined : result.error
    };
}

/**
 * Generic validation function for any schema
 */
export function validateWithSchema<T>(
    schema: z.ZodSchema<T>,
    data: unknown
): {
    success: boolean;
    data?: T;
    error?: z.ZodError;
} {
    const result = schema.safeParse(data);
    return {
        success: result.success,
        data: result.success ? result.data : undefined,
        error: result.success ? undefined : result.error
    };
}

// ========================================
// ERROR FORMATTING
// ========================================

/**
 * Formats Zod validation errors into a readable string
 */
export function formatValidationError(error: z.ZodError): string {
    const issues = error.issues.map(issue => {
        const path = issue.path.join('.');
        return `${path ? `${path}: ` : ''}${issue.message}`;
    });
    return `Validation failed: ${issues.join(', ')}`;
}

// ========================================
// TYPE EXPORTS
// ========================================

export type TwilioStartMessage = z.infer<typeof TwilioStartMessageSchema>;
export type TwilioMediaMessage = z.infer<typeof TwilioMediaMessageSchema>;
export type TwilioStopMessage = z.infer<typeof TwilioStopMessageSchema>;
export type TwilioMarkMessage = z.infer<typeof TwilioMarkMessageSchema>;
export type TwilioConnectedMessage = z.infer<typeof TwilioConnectedMessageSchema>;
export type TwilioMessage = z.infer<typeof TwilioMessageSchema>;

export type LocalStartMessage = z.infer<typeof LocalStartMessageSchema>;
export type LocalAudioMessage = z.infer<typeof LocalAudioMessageSchema>;
export type LocalTextMessage = z.infer<typeof LocalTextMessageSchema>;
export type LocalEndSessionMessage = z.infer<typeof LocalEndSessionMessageSchema>;
export type LocalHeartbeatMessage = z.infer<typeof LocalHeartbeatMessageSchema>;
export type LocalTurnCompleteMessage = z.infer<typeof LocalTurnCompleteMessageSchema>;
export type LocalRequestSummaryMessage = z.infer<typeof LocalRequestSummaryMessageSchema>;
export type LocalAudioResponseMessage = z.infer<typeof LocalAudioResponseMessageSchema>;
export type LocalMessage = z.infer<typeof LocalMessageSchema>;

export type SessionConfig = z.infer<typeof SessionConfigSchema>;