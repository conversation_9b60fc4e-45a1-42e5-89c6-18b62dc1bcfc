import { endSession, scheduleRecovery } from './session-utils';
import { websocketLogger } from '../utils/logger';
import { globalHeartbeatManager } from './heartbeat-manager';
import pkg from '@google/genai';
const { Modality } = pkg;
import {
    SessionStartupOptimizer,
    FastConfigLoader,
    ConnectionQualityMonitor
} from './performance-optimizations';
import type {
    WebSocketConnection,
    FlowDependencies,
    ConnectionData,
    SessionData,
    LocalStartMessage,
    LocalAudioMessage,
    LocalTextMessage
} from '../types/websocket';
import type { WebSocket } from 'ws';
import {
    validateLocalMessage,
    formatValidationError
} from './message-schemas';

// Performance optimization instances
const startupOptimizer = new SessionStartupOptimizer({
    enableFastStart: true,
    skipNonEssentialChecks: false,
    preloadGeminiSession: false,
    parallelInitialization: true,
    timeoutMs: 8000 // Reduced from 15+ seconds to 8 seconds
});

const configLoader = new FastConfigLoader();
const qualityMonitor = new ConnectionQualityMonitor();

// Common local testing flow handler (for both inbound and outbound testing)
export function handleLocalTestingFlow(connection: WebSocketConnection, deps: FlowDependencies): void {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;
    
    if (enableDetailedLogging) {
        websocketLogger.debug('HANDLELOCALTESTINGFLOW CALLED!', { flowType: deps.flowType });
    }
    
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        summaryManager,
        lifecycleManager,
        recoveryManager,
        transcriptionManager,
        flowType,
        getSessionConfig,
        isIncomingCall,
        SUMMARY_GENERATION_PROMPT
    } = deps;

    const sessionId = `${flowType}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    if (enableDetailedLogging) {
        websocketLogger.debug('About to log testing session started', { sessionId });
    }
    websocketLogger.info(`${flowType.toUpperCase()} testing session started`, { sessionId });
    if (enableDetailedLogging) {
        websocketLogger.debug(`Setting up message handler for ${flowType} testing`, { sessionId });
        websocketLogger.debug('Step 1: About to check WebSocket state', { sessionId });
    }

    try {
        if (enableDetailedLogging) {
            websocketLogger.debug('Connection object inspected', { sessionId, hasConnection: !!connection });
            websocketLogger.debug('Connection keys', { sessionId, keys: Object.keys(connection) });
            websocketLogger.debug('Socket object present', { sessionId, hasSocket: !!connection.socket });
        }

        // Try different possible WebSocket references
        const ws = connection.socket || connection;
        if (enableDetailedLogging) {
            websocketLogger.debug('Using WebSocket', { sessionId, hasWs: !!ws });
            websocketLogger.debug('WebSocket readyState', { sessionId, readyState: (ws as WebSocket).readyState });
            websocketLogger.debug('WebSocket protocol', { sessionId, protocol: (ws as WebSocket).protocol });
            websocketLogger.debug('Step 3: About to declare variables', { sessionId });
        }
    } catch (wsError) {
        websocketLogger.error('Error checking WebSocket state', wsError instanceof Error ? wsError : new Error(String(wsError)), sessionId);
        if (enableDetailedLogging) {
            websocketLogger.error('Error stack trace', { stack: (wsError as Error).stack }, sessionId);
        }
        // Don't return early - continue with handler setup
        if (enableDetailedLogging) {
            websocketLogger.debug('Continuing with handler setup despite WebSocket error', { sessionId });
        }
    }

    let geminiSession: any = null;
    let isSessionActive = false;
    if (enableDetailedLogging) {
        websocketLogger.debug('Step 4: Variables declared, about to attach message handler', { sessionId });
    }

    // Use the correct WebSocket reference
    const ws = (connection.socket || connection) as WebSocket;
    if (enableDetailedLogging) {
        websocketLogger.debug('Attaching message handler', { sessionId, hasWs: !!ws });
    }

    // Store event listeners for cleanup
    const eventListeners = new Map<string, (...args: any[]) => void>();

    const messageHandler = async (message: Buffer | string) => {
        if (enableDetailedLogging) {
            websocketLogger.debug('MESSAGE HANDLER CALLED!', { sessionId });
            websocketLogger.debug('MESSAGE HANDLER CALLED!', { sessionId });
            websocketLogger.debug('Raw message received', { sessionId, preview: message.toString().substring(0, 200) });
        }
        try {
            const rawData = JSON.parse(message.toString());
            
            // Validate message with Zod schema
            const validation = validateLocalMessage(rawData);
            if (!validation.success) {
                websocketLogger.warn('❌ Invalid local message format', {
                    sessionId,
                    error: formatValidationError(validation.error!),
                    rawData
                });
                return;
            }

            const data = validation.data!;
            
            if (enableDetailedLogging) {
                websocketLogger.debug('✅ Local message validated', { sessionId, messageType: data.type });
                websocketLogger.debug(`Parsed message type: ${data.type}`, { sessionId });
                websocketLogger.debug('Message data keys', { sessionId, keys: Object.keys(data) });
                websocketLogger.debug('===== MESSAGE SWITCH DEBUG =====', { sessionId });
                websocketLogger.debug(`data.type = "${data.type}"`, { sessionId });
                websocketLogger.debug('About to enter switch statement', { sessionId });
                websocketLogger.debug('=====================================', { sessionId });
            }

            switch (data.type) {
            case 'start-session': {
                if (enableDetailedLogging) {
                    websocketLogger.debug('ENTERING START-SESSION HANDLER', { sessionId, flowType });
                }
                const sessionData = await handleLocalStartSession(sessionId, data, deps, connection, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager);
                geminiSession = sessionData.geminiSession;
                isSessionActive = sessionData.isSessionActive;
                if (enableDetailedLogging) {
                    websocketLogger.debug('START-SESSION HANDLER COMPLETED', { sessionId, hasGeminiSession: !!geminiSession, isSessionActive });
                }
                break;
            }

            case 'audio-data':
                // Normalize legacy type name to the preferred 'audio'
                data.type = 'audio';
                // Fall through to 'audio' case
            case 'audio':
                await handleAudioData(sessionId, data, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager, flowType);
                break;

            case 'text-message':
                await handleTextMessage(sessionId, data, geminiSession, isSessionActive, deps);
                break;

            case 'turn-complete':
                await handleTurnComplete(sessionId, geminiSession, isSessionActive, deps);
                break;

            case 'end-session':
                await handleEndSession(sessionId, deps, activeConnections, lifecycleManager);
                break;

            case 'request-summary':
                await handleRequestSummary(sessionId, deps, activeConnections, summaryManager, contextManager);
                break;

            case 'heartbeat':
                // Handle heartbeat messages silently - just acknowledge
                websocketLogger.debug(`Heartbeat received for ${flowType} testing`, { sessionId });
                break;

            case 'audio-response':
                // Handle audio-response events (frontend might be echoing back audio)
                websocketLogger.debug(`Audio response received for ${flowType} testing`, { sessionId });
                // Don't process these - they're just echoes from the frontend
                break;

            default:
                websocketLogger.warn(`Unknown ${flowType} testing event: ${(data as any).type}`, { sessionId });
            }

        } catch (error) {
            websocketLogger.error(`Error processing ${flowType} testing message`, error instanceof Error ? error : new Error(String(error)), sessionId);
        }
    };

    // Store handlers for cleanup
    eventListeners.set('message', messageHandler);
    websocketLogger.debug('ATTACHING MESSAGE HANDLER TO WebSocket', { sessionId, readyState: ws.readyState });
    ws.on('message', messageHandler);

    const closeHandler = async (code: number, reason: Buffer) => {
        websocketLogger.info(`${flowType.toUpperCase()} testing connection closed`, {
            sessionId,
            code,
            reason: reason ? reason.toString() : 'No reason'
        });

        // Stop heartbeat monitoring
        globalHeartbeatManager.stopHeartbeat(sessionId);
        // This is a user-initiated session end via connection close
        const connectionData = activeConnections.get(sessionId);
        if (connectionData) {
            // Clean up Deepgram transcription
            if (connectionData.deepgramConnection) {
                deps.transcriptionManager.closeTranscription(sessionId);
            }

            if (deps.lifecycleManager) {
                await deps.lifecycleManager.endSession(sessionId, connectionData, 'user_close_testing');
            } else {
                endSession(sessionId, deps, 'connection_closed');
            }
        } else {
            endSession(sessionId, deps, 'connection_closed');
        }

        // Clean up event listeners
        for (const [event, handler] of eventListeners) {
            ws.removeListener(event, handler as any);
        }
        eventListeners.clear();
    };

    const errorHandler = async (error: Error) => {
        websocketLogger.error(`${flowType.toUpperCase()} testing error`, { sessionId, error });
        websocketLogger.error('Error stack', { sessionId, stack: error.stack });
        // WebSocket error in testing - try to recover session instead of ending it
        const connectionData = activeConnections.get(sessionId);
        if (connectionData && recoveryManager && contextManager.canRecover(sessionId)) {
            websocketLogger.info('Testing WebSocket error detected, attempting session recovery', { sessionId });
            contextManager.markSessionInterrupted(sessionId, 'testing_websocket_error');
            // Don't end session immediately - let recovery manager handle it
            scheduleRecovery(sessionId, 'testing_websocket_error', recoveryManager, activeConnections);
        } else {
            // Clean up Deepgram transcription before ending session
            if (connectionData && connectionData.deepgramConnection) {
                deps.transcriptionManager.closeTranscription(sessionId);
            }
            // Only end session if recovery is not possible
            endSession(sessionId, { ...deps, transcriptionManager }, 'connection_error');
        }
    };

    // Register all event listeners and store for cleanup
    eventListeners.set('close', closeHandler);
    eventListeners.set('error', errorHandler);
    ws.on('close', closeHandler);
    ws.on('error', errorHandler);
}

// Helper functions for message handling
async function handleLocalStartSession(
    sessionId: string,
    data: LocalStartMessage,
    deps: FlowDependencies,
    connection: WebSocketConnection,
    ws: WebSocket,
    flowType: string,
    isIncomingCall: boolean,
    getSessionConfig: (callSid?: string) => any,
    activeConnections: Map<string, ConnectionData>,
    healthMonitor: any,
    lifecycleManager: any,
    sessionManager: any
): Promise<SessionData> {
    websocketLogger.debug('===== OPTIMIZED START-SESSION =====', { sessionId });
    websocketLogger.info('Starting testing session with performance optimizations', { sessionId, flowType });

    // Start performance tracking
    startupOptimizer.startTracking(sessionId);
    qualityMonitor.startMonitoring(sessionId, ws);

    try {
        // Optimized configuration loading with caching
        websocketLogger.debug('Loading config with optimization', { sessionId, flowType });
        websocketLogger.debug('getSessionConfig function type', { sessionId, type: typeof getSessionConfig });

        // Test the getSessionConfig function directly first
        websocketLogger.debug('Testing getSessionConfig directly', { sessionId });
        try {
            const directConfig = await getSessionConfig();
            websocketLogger.debug('Direct getSessionConfig result', {
                sessionId,
                hasResult: !!directConfig,
                resultType: typeof directConfig,
                resultKeys: directConfig ? Object.keys(directConfig) : [],
                hasAiInstructions: !!directConfig?.aiInstructions,
                aiInstructionsLength: directConfig?.aiInstructions?.length || 0
            });
        } catch (directError) {
            websocketLogger.error('Error calling getSessionConfig directly', directError as Error, sessionId);
        }

        let sessionConfig = await configLoader.loadConfig(
            sessionId,
            getSessionConfig,
            true // Use cache for better performance
        );
        startupOptimizer.markConfigLoaded(sessionId);

        websocketLogger.debug('Optimized config loaded', { sessionId, loaded: !!sessionConfig });

        if (sessionConfig) {
            websocketLogger.debug('Initial session config', {
                hasAiInstructions: !!sessionConfig.aiInstructions,
                aiInstructionsLength: sessionConfig.aiInstructions?.length || 0,
                voice: sessionConfig.voice,
                model: sessionConfig.model,
                scriptType: sessionConfig.scriptType,
                scriptId: sessionConfig.scriptId
            });
        } else {
            websocketLogger.error(`getSessionConfig returned null/undefined for ${flowType}`, {}, sessionId);
        }

        // 🔍 ENHANCED DEBUGGING: Log detailed session config information
        websocketLogger.debug('DETAILED SESSION CONFIG DEBUG', {
            hasSessionConfig: !!sessionConfig,
            sessionConfigKeys: sessionConfig ? Object.keys(sessionConfig) : [],
            hasAiInstructions: !!sessionConfig?.aiInstructions,
            aiInstructionsType: typeof sessionConfig?.aiInstructions,
            aiInstructionsLength: sessionConfig?.aiInstructions?.length || 0,
            aiInstructionsPreview: sessionConfig?.aiInstructions?.substring(0, 100) || 'N/A',
            flowType: flowType,
            isIncomingCall: isIncomingCall
        });

        // CRITICAL VALIDATION: Ensure AI instructions are present before proceeding
        if (!sessionConfig || !sessionConfig.aiInstructions || sessionConfig.aiInstructions.trim().length === 0) {
            const errorMessage = `No AI instructions available for ${flowType} session. Cannot create Gemini session without proper system instructions.`;
            websocketLogger.error(`VALIDATION FAILED: ${errorMessage}`, {}, sessionId);
            websocketLogger.error('Session config details', {
                hasConfig: !!sessionConfig,
                configKeys: sessionConfig ? Object.keys(sessionConfig) : [],
                aiInstructions: sessionConfig?.aiInstructions || 'undefined'
            }, sessionId);

            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: errorMessage,
                    code: 'MISSING_AI_INSTRUCTIONS',
                    debug: {
                        flowType: flowType,
                        hasConfig: !!sessionConfig,
                        configKeys: sessionConfig ? Object.keys(sessionConfig) : []
                    }
                }));
            }

            // Cleanup and return early
            qualityMonitor.stopMonitoring(sessionId);
            startupOptimizer.cleanup(sessionId);
            activeConnections.delete(sessionId);

            return { geminiSession: null, isSessionActive: false };
        }

        websocketLogger.info('AI instructions validation passed', { sessionId, instructionLength: sessionConfig.aiInstructions.length });
        const instructionPreview = sessionConfig.aiInstructions.substring(0, 150) + (sessionConfig.aiInstructions.length > 150 ? '...' : '');
        websocketLogger.debug('Instructions preview', { sessionId, preview: instructionPreview });

        // Allow override from client for testing - use campaign script as-is
        if (data.aiInstructions) {
            sessionConfig.aiInstructions = data.aiInstructions;
        }
        if (data.voice) {
            sessionConfig.voice = deps.voiceManager.getValidGeminiVoice(data.voice);
        }
        if (data.model) {
            sessionConfig.model = deps.modelManager.getValidGeminiModel(data.model);
        }
        if (data.scriptId) {
            // Load specific script for testing
            try {
                const testConfig = await deps.scriptManager.getScriptConfig(data.scriptId, isIncomingCall);
                if (testConfig) {
                    sessionConfig = {
                        ...testConfig,
                        aiInstructions: `[TESTING MODE] ${testConfig.aiInstructions}`,
                        isTestMode: true
                    };
                }
            } catch (error) {
                websocketLogger.warn(`Error loading test script ${data.scriptId}`, { error: error instanceof Error ? error.message : String(error) }, sessionId);
            }
        }

        // Store enhanced connection data
        const connectionData: ConnectionData = {
            ws: (connection.socket || connection) as WebSocket, // Standardized WebSocket property name
            localWs: (connection.socket || connection) as WebSocket,
            sessionId,
            isSessionActive: false,
            summaryRequested: false,
            summaryReceived: false,
            summaryText: '',
            conversationLog: [],
            fullTranscript: [],
            speechTranscript: [],
            isIncomingCall,
            sessionType: 'local_test',
            flowType,
            sessionStartTime: Date.now(),
            lastActivity: Date.now(),
            targetName: sessionConfig.targetName || 'Test Contact',
            targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
            originalAIInstructions: sessionConfig.aiInstructions,
            scriptId: sessionConfig.scriptId,
            isTestMode: true,
            // CRITICAL: Add missing turn management properties
            lastAIResponse: Date.now(), // Track AI responsiveness
            responseTimeouts: 0, // Count consecutive timeouts
            connectionQuality: 'good', // Track connection quality
            lastContextSave: Date.now(), // For periodic context saving
            contextSaveInterval: null // For periodic context saving
        };
        activeConnections.set(sessionId, connectionData);

        // Track connection health
        healthMonitor.trackConnection(sessionId, 'connected', {
            flowType,
            isTestMode: true,
            scriptId: sessionConfig.scriptId
        });

        // Optimized parallel session creation
        websocketLogger.debug('Starting optimized Gemini session creation', { sessionId });

        const sessionStartTime = Date.now();
        let geminiSession: any = null;
        let isSessionActive = false;

        try {
            // Use the correct model and voice from environment/managers
            const correctModel = sessionConfig.model || deps.GEMINI_DEFAULT_MODEL;
            const correctVoice = sessionConfig.voice || deps.GEMINI_DEFAULT_VOICE;
            websocketLogger.debug('OPTIMIZED: Using configuration', { sessionId, model: correctModel, voice: correctVoice });

            // Create Gemini session first
            websocketLogger.debug('Creating Gemini session with optimized timeout', { sessionId });
            geminiSession = await deps.sessionManager.createGeminiSession(sessionId, {
                model: correctModel,
                voice: correctVoice,
                aiInstructions: sessionConfig.aiInstructions
            }, connectionData);

            // Initialize transcription in parallel (non-blocking)
            if (deps.transcriptionManager) {
                websocketLogger.debug('Initializing transcription in background', { sessionId });
                deps.transcriptionManager.initializeLocalTranscription(sessionId, connectionData as any)
                    .then((transcriptionResult) => {
                        if (transcriptionResult) {
                            websocketLogger.info('Background transcription initialized', { sessionId });
                        }
                    })
                    .catch((error) => {
                        websocketLogger.warn('Background transcription initialization failed', { error: error instanceof Error ? error.message : String(error) }, sessionId);
                    });
            }

            startupOptimizer.markGeminiSessionCreated(sessionId);

            if (geminiSession) {
                isSessionActive = true;
                websocketLogger.info('Optimized Gemini session created in parallel', { sessionId });
            } else {
                throw new Error('Failed to create Gemini session via optimized parallel initialization');
            }

            // Session is now created and configured by sessionManager
            // No need for additional setup - sessionManager handles Live API configuration

            // Start optimized WebSocket heartbeat monitoring for local testing
            globalHeartbeatManager.startHeartbeat(
                sessionId,
                ws,
                deps.config?.websocket?.heartbeatInterval || 30000,
                deps.config?.websocket?.heartbeatTimeout || 60000,
                (sessionId: string, ws: WebSocket) => {
                    websocketLogger.warn('Optimized heartbeat timeout - ending session', { sessionId });
                    qualityMonitor.stopMonitoring(sessionId);
                    startupOptimizer.cleanup(sessionId);
                    endSession(sessionId, { activeConnections, lifecycleManager } as any, 'heartbeat_timeout');
                }
            );

        } catch (error) {
            websocketLogger.error('Error creating DIRECT Gemini session', error instanceof Error ? error : new Error(String(error)), sessionId);
            geminiSession = null;
        }

        if (geminiSession) {
            // Initialize session lifecycle for local testing
            lifecycleManager.initializeSession(sessionId, { connectionData, sessionConfig });
            websocketLogger.info('Session lifecycle started for testing', { sessionId, flowType });
            
            // Initialize Deepgram transcription for local testing
            try {
                const dgConnection = await deps.transcriptionManager.initializeLocalTranscription(sessionId, connectionData as any);
                if (dgConnection) {
                    connectionData.deepgramConnection = dgConnection as any;
                    websocketLogger.info('Local Deepgram transcription initialized for testing', { sessionId, flowType });
                }
            } catch (error) {
                websocketLogger.warn('Failed to initialize local Deepgram transcription', { error: error instanceof Error ? error.message : String(error) }, sessionId);
            }

            // Mark session as ready and get performance metrics
            const performanceMetrics = startupOptimizer.markSessionReady(sessionId);

            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-started',
                    sessionId: sessionId,
                    flowType: flowType,
                    scriptId: sessionConfig.scriptId,
                    config: {
                        voice: sessionConfig.voice,
                        model: sessionConfig.model,
                        isIncomingCall: isIncomingCall,
                        transcriptionEnabled: !!connectionData.deepgramConnection
                    },
                    performance: performanceMetrics ? {
                        totalStartupTime: performanceMetrics.totalTime,
                        optimized: true
                    } : undefined
                }));
            }
            websocketLogger.info('Testing session started successfully', { sessionId, flowType: flowType.toUpperCase() });
        } else {
            websocketLogger.error('Gemini session creation returned null/undefined', {}, sessionId);
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: 'Failed to create Gemini session'
                }));
            }
        }

        return { geminiSession, isSessionActive };

    } catch (startSessionError) {
        websocketLogger.error(`Critical error in optimized start-session for ${flowType}`, startSessionError instanceof Error ? startSessionError : new Error(String(startSessionError)), sessionId);

        // Cleanup performance monitoring on error
        qualityMonitor.stopMonitoring(sessionId);
        startupOptimizer.cleanup(sessionId);

        try {
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: `Optimized session start failed: ${(startSessionError as Error).message}`,
                    optimized: true
                }));
            }
        } catch (sendError) {
            websocketLogger.error('Failed to send error message', sendError instanceof Error ? sendError : new Error(String(sendError)), sessionId);
        }
        return { geminiSession: null, isSessionActive: false };
    }
}

async function handleAudioData(
    sessionId: string, 
    data: LocalAudioMessage, 
    geminiSession: any, 
    isSessionActive: boolean, 
    deps: FlowDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    lifecycleManager: any, 
    recoveryManager: any, 
    flowType: string
): Promise<void> {
    if (geminiSession && isSessionActive && (data.audioData || data.audio)) {
        try {
            // Update activity for session persistence
            lifecycleManager.transitionState(sessionId, 'active', 'audio_received');

            // Handle browser audio data (support both audioData and audio fields)
            const base64Audio = data.audioData || data.audio || '';
            websocketLogger.debug('About to call sendBrowserAudioToGemini', { sessionId, audioSize: base64Audio.length, hasGeminiSession: !!geminiSession });
            await deps.sessionManager.sendBrowserAudioToGemini(sessionId, geminiSession, base64Audio);

            // Send audio to Deepgram for transcription (local testing)
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && connectionData.deepgramConnection && deps.transcriptionManager) {
                try {
                    const audioBuffer = Buffer.from(base64Audio, 'base64');
                    deps.transcriptionManager.sendAudioToTranscription(sessionId, audioBuffer);
                } catch (dgError) {
                    websocketLogger.warn('Local Deepgram send error', { error: dgError instanceof Error ? dgError.message : String(dgError) }, sessionId);
                }
            }

        } catch (error) {
            websocketLogger.error(`Error processing ${flowType} testing audio`, error instanceof Error ? error : new Error(String(error)), sessionId);

            // Check if we need to recover the session
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(sessionId, activeConnections)) {
                websocketLogger.info('Audio processing failed in testing, attempting session recovery', { sessionId, flowType });
                await recoveryManager.recoverSession(sessionId, 'audio_processing_error', activeConnections);
            }
        }
    }
}

async function handleTextMessage(
    sessionId: string, 
    data: LocalTextMessage, 
    geminiSession: any, 
    isSessionActive: boolean, 
    deps: FlowDependencies
): Promise<void> {
    if (geminiSession && isSessionActive && data.text) {
        try {
            websocketLogger.debug('Received text message', { sessionId, text: data.text });
            await deps.sessionManager.sendTextToGemini(sessionId, geminiSession, data.text);
        } catch (error) {
            websocketLogger.error('Error sending text to Gemini', error instanceof Error ? error : new Error(String(error)), sessionId);
        }
    }
}

async function handleTurnComplete(
    sessionId: string, 
    geminiSession: any, 
    isSessionActive: boolean, 
    deps: FlowDependencies
): Promise<void> {
    // REMOVED: Turn complete handling for Live API
    // The Live API handles conversation turns automatically with Voice Activity Detection (VAD)
    // No manual turn management is needed - the model will respond when appropriate
    websocketLogger.debug('Turn complete signal received - Live API handles this automatically', { sessionId });
}

async function handleEndSession(
    sessionId: string, 
    deps: FlowDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    lifecycleManager: any
): Promise<void> {
    websocketLogger.info('Ending testing session - USER INITIATED', { sessionId });
    // This is a user-initiated session end for testing
    const endConnectionData = activeConnections.get(sessionId);
    if (endConnectionData) {
        // Clean up Deepgram transcription
        if (endConnectionData.deepgramConnection) {
            deps.transcriptionManager.closeTranscription(sessionId);
        }

        if (lifecycleManager) {
            await lifecycleManager.endSession(sessionId, endConnectionData, 'user_end_testing');
        } else {
            await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
        }
    } else {
        await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
    }
}

async function handleRequestSummary(
    sessionId: string, 
    _deps: FlowDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    summaryManager: any, 
    contextManager: any
): Promise<void> {
    websocketLogger.info('Manual summary requested for testing', { sessionId });
    const summaryConnectionData = activeConnections.get(sessionId);
    if (summaryConnectionData && summaryManager) {
        await summaryManager.requestSummary(sessionId, summaryConnectionData, contextManager);
    }
}