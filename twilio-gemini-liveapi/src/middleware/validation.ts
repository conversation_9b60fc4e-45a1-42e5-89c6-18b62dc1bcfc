import { FastifyRequest, FastifyReply } from 'fastify';
import { z } from 'zod';
import { logger } from '../utils/logger.js';

/**
 * Generic validation middleware factory for Fastify routes
 */
export function validateBody<T extends z.ZodType>(schema: T) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const result = schema.safeParse(request.body);
      
      if (!result.success) {
        const errors = result.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code
        }));
        
        logger.warn('Request validation failed', {
          url: request.url,
          method: request.method,
          errors,
          body: request.body
        });
        
        return reply.status(400).send({
          error: 'Validation failed',
          details: errors
        });
      }
      
      // Replace request.body with validated data
      request.body = result.data;
    } catch (error) {
      logger.error('Validation middleware error:', error as Error);
      return reply.status(500).send({
        error: 'Internal validation error'
      });
    }
  };
}

/**
 * Validate query parameters
 */
export function validateQuery<T extends z.ZodType>(schema: T) {
  return async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const result = schema.safeParse(request.query);
      
      if (!result.success) {
        const errors = result.error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code
        }));
        
        logger.warn('Query validation failed', {
          url: request.url,
          method: request.method,
          errors,
          query: request.query
        });
        
        return reply.status(400).send({
          error: 'Query validation failed',
          details: errors
        });
      }
      
      // Replace request.query with validated data
      request.query = result.data;
    } catch (error) {
      logger.error('Query validation middleware error:', error as Error);
      return reply.status(500).send({
        error: 'Internal validation error'
      });
    }
  };
}

/**
 * Validate WebSocket messages
 */
export function validateWebSocketMessage<T extends z.ZodType>(
  schema: T,
  message: unknown
): { success: true; data: z.infer<T> } | { success: false; error: string } {
  try {
    const result = schema.safeParse(message);
    
    if (!result.success) {
      const errors = result.error.errors.map(err => 
        `${err.path.join('.')}: ${err.message}`
      ).join(', ');
      
      return {
        success: false,
        error: `Validation failed: ${errors}`
      };
    }
    
    return {
      success: true,
      data: result.data
    };
  } catch (error) {
    return {
      success: false,
      error: `Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Sanitize string input to prevent XSS and injection attacks
 */
export function sanitizeString(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Validate and sanitize phone numbers
 */
export function validatePhoneNumber(phone: string): { valid: boolean; normalized?: string; error?: string } {
  try {
    // Remove all non-digit characters except +
    const cleaned = phone.replace(/[^\d+]/g, '');
    
    // Check if it starts with + and has the right length
    if (!cleaned.startsWith('+')) {
      return { valid: false, error: 'Phone number must start with +' };
    }
    
    // Check length (E.164 format: +1 to +15 digits)
    if (cleaned.length < 8 || cleaned.length > 16) {
      return { valid: false, error: 'Phone number length invalid' };
    }
    
    // Check if all characters after + are digits
    const digits = cleaned.slice(1);
    if (!/^\d+$/.test(digits)) {
      return { valid: false, error: 'Phone number contains invalid characters' };
    }
    
    return { valid: true, normalized: cleaned };
  } catch (error) {
    return { valid: false, error: 'Phone number validation error' };
  }
}

/**
 * Rate limiting validation
 */
export function validateRateLimit(
  identifier: string,
  limit: number,
  windowMs: number,
  storage: Map<string, { count: number; resetTime: number }>
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const key = identifier;
  
  const current = storage.get(key);
  
  if (!current || now > current.resetTime) {
    // Reset or initialize
    const resetTime = now + windowMs;
    storage.set(key, { count: 1, resetTime });
    return { allowed: true, remaining: limit - 1, resetTime };
  }
  
  if (current.count >= limit) {
    return { allowed: false, remaining: 0, resetTime: current.resetTime };
  }
  
  current.count++;
  storage.set(key, current);
  return { allowed: true, remaining: limit - current.count, resetTime: current.resetTime };
}

/**
 * Clean up expired rate limit entries
 */
export function cleanupRateLimit(storage: Map<string, { count: number; resetTime: number }>): void {
  const now = Date.now();
  for (const [key, value] of storage.entries()) {
    if (now > value.resetTime) {
      storage.delete(key);
    }
  }
}

/**
 * Validate file upload size and type
 */
export function validateFileUpload(
  file: { size: number; mimetype: string },
  maxSize: number,
  allowedTypes: string[]
): { valid: boolean; error?: string } {
  if (file.size > maxSize) {
    return { valid: false, error: `File too large. Maximum size: ${maxSize} bytes` };
  }
  
  if (!allowedTypes.includes(file.mimetype)) {
    return { valid: false, error: `Invalid file type. Allowed: ${allowedTypes.join(', ')}` };
  }
  
  return { valid: true };
}
