import { Mock<PERSON>udioBuffer } from './audio-types';
import { audioLogger } from '../utils/logger';

/**
 * Core audio conversion utilities for Twilio μ-law ↔ Gemini PCM
 * Handles format conversion, resampling, and basic audio transformations
 */
export class AudioConverter {
    private ULAW_TO_LINEAR: Int16Array;

    constructor() {
        // μ-law to linear conversion table (pre-computed for performance)
        this.ULAW_TO_LINEAR = new Int16Array(256);
        this.initializeUlawTable();
    }

    /**
     * Initialize μ-law to linear conversion table
     */
    private initializeUlawTable(): void {
        for (let i = 0; i < 256; i++) {
            const ulaw = ~i;
            let t = ((ulaw & 0x0F) << 3) + 0x84;
            t <<= ((ulaw & 0x70) >> 4);
            this.ULAW_TO_LINEAR[i] = (ulaw & 0x80) ? (0x84 - t) : (t - 0x84);
        }
    }

    /**
     * Converts Twilio's G.711 μ-law audio to Int16 PCM format
     * @param audioBuffer - Input audio data in μ-law format from <PERSON><PERSON><PERSON>
     * @returns Converted audio data in Int16 PCM format
     */
    convertUlawToPCM(audioBuffer: Buffer): Buffer {
        try {
            if (!audioBuffer || !Buffer.isBuffer(audioBuffer) || audioBuffer.length === 0) {
                return Buffer.alloc(0);
            }

            // Convert G.711 μ-law to 16-bit PCM
            const int16Buffer = Buffer.alloc(audioBuffer.length * 2);
            for (let i = 0; i < audioBuffer.length; i++) {
                // Ensure ulaw sample is in valid range (0-255)
                const ulawSample = Math.max(0, Math.min(255, audioBuffer[i] || 0));
                const pcmSample = this.ULAW_TO_LINEAR[ulawSample] || 0;
                int16Buffer.writeInt16LE(pcmSample, i * 2);
            }

            return int16Buffer;
        } catch (error) {
            console.error('❌ Error in μ-law to PCM conversion:', error);
            // Fallback to basic conversion
            return this.basicUlawToPCM(audioBuffer);
        }
    }

    /**
     * Basic μ-law to PCM conversion without enhancements (fallback)
     */
    basicUlawToPCM(audioBuffer: Buffer): Buffer {
        if (!audioBuffer || !Buffer.isBuffer(audioBuffer) || audioBuffer.length === 0) {
            return Buffer.alloc(0);
        }
        
        const int16Buffer = Buffer.alloc(audioBuffer.length * 2);
        for (let i = 0; i < audioBuffer.length; i++) {
            const ulawSample = Math.max(0, Math.min(255, audioBuffer[i] || 0));
            const pcmSample = this.ULAW_TO_LINEAR[ulawSample] || 0;
            int16Buffer.writeInt16LE(pcmSample, i * 2);
        }
        return int16Buffer;
    }

    /**
     * Enhanced conversion of Gemini PCM audio back to μ-law for Twilio
     * @param base64Audio - Base64 encoded PCM audio from Gemini (24kHz) OR Buffer for testing
     * @returns Base64 encoded μ-law audio for Twilio (8kHz) OR Buffer for testing
     */
    convertPCMToUlaw(base64Audio: string | Buffer): string | Buffer {
        try {
            console.log(`🔄 Converting Gemini audio to μ-law: input type=${typeof base64Audio}, length=${base64Audio?.length || 0}`);

            // Handle both base64 string and Buffer input for testing compatibility
            const pcmBuffer = Buffer.isBuffer(base64Audio) ? base64Audio : Buffer.from(base64Audio, 'base64');
            const returnAsBuffer = Buffer.isBuffer(base64Audio);

            console.log(`🔄 PCM buffer created: length=${pcmBuffer.length} bytes`);

            // Validate PCM buffer
            if (pcmBuffer.length === 0) {
                console.warn('⚠️ Empty PCM buffer received');
                return returnAsBuffer ? Buffer.alloc(0) : '';
            }

            // Convert PCM buffer to Float32Array for processing
            const float32Array = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < float32Array.length; i++) {
                float32Array[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }

            console.log(`🔄 Float32 array created: length=${float32Array.length} samples`);

            // Check for valid audio data
            const hasValidAudio = float32Array.some(sample => Math.abs(sample) > 0.001);
            if (!hasValidAudio) {
                console.warn('⚠️ Audio appears to be silent or very quiet');
            }

            // Downsample from 24kHz to 8kHz
            const downsampled = this.downsample24kTo8k(float32Array);

            // Convert to μ-law
            const ulawBuffer = Buffer.alloc(downsampled.length);

            for (let i = 0; i < downsampled.length; i++) {
                // Ensure proper range and convert to μ-law
                const pcmSample = Math.max(-32767, Math.min(32767, Math.round(downsampled[i] * 32767)));
                ulawBuffer[i] = this.linearToUlaw(pcmSample);
            }

            return returnAsBuffer ? ulawBuffer : ulawBuffer.toString('base64');
        } catch (error) {
            console.error('❌ Error converting PCM to μ-law:', error);
            return this.fallbackPCMToUlaw(base64Audio);
        }
    }

    /**
     * Simple PCM to μ-law conversion for testing (no resampling)
     * @param pcmBuffer - PCM16 audio buffer at 8kHz
     * @returns μ-law encoded buffer
     */
    pcmToUlaw(pcmBuffer: Buffer): Buffer {
        if (!Buffer.isBuffer(pcmBuffer)) {
            throw new Error('Input must be a Buffer');
        }

        const ulawBuffer = Buffer.alloc(Math.floor(pcmBuffer.length / 2));
        
        for (let i = 0; i < ulawBuffer.length; i++) {
            const pcmSample = pcmBuffer.readInt16LE(i * 2);
            ulawBuffer[i] = this.linearToUlaw(pcmSample);
        }

        return ulawBuffer;
    }

    /**
     * Converts linear PCM sample to μ-law
     * @param pcm - Linear PCM sample
     * @returns μ-law encoded sample
     */
    linearToUlaw(pcm: number): number {
        const BIAS = 0x84;
        const CLIP = 32635;

        const sign = (pcm >> 8) & 0x80;
        if (sign !== 0) {
            pcm = -pcm;
        }
        if (pcm > CLIP) {
            pcm = CLIP;
        }

        pcm += BIAS;
        let exponent = 7;
        let expMask = 0x4000;

        for (let i = 0; i < 8; i++) {
            if ((pcm & expMask) !== 0) {
                break;
            }
            exponent--;
            expMask >>= 1;
        }

        const mantissa = (pcm >> (exponent + 3)) & 0x0F;
        const ulaw = ~(sign | (exponent << 4) | mantissa);

        return ulaw & 0xFF;
    }

    /**
     * Fallback PCM to μ-law conversion
     * @param base64Audio - Base64 encoded PCM audio
     * @returns Base64 encoded μ-law audio
     */
    fallbackPCMToUlaw(base64Audio: string | Buffer): string | Buffer {
        try {
            // Handle both base64 string and Buffer input for testing compatibility
            const pcmBuffer = Buffer.isBuffer(base64Audio) ? base64Audio : Buffer.from(base64Audio, 'base64');
            const returnAsBuffer = Buffer.isBuffer(base64Audio);
            const float32Array = new Float32Array(pcmBuffer.length / 2);

            for (let i = 0; i < float32Array.length; i++) {
                float32Array[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }

            const downsampled = this.downsample24kTo8kSimple(float32Array);
            const ulawBuffer = Buffer.alloc(downsampled.length);

            for (let i = 0; i < downsampled.length; i++) {
                const pcmSample = Math.round(downsampled[i] * 32767);
                ulawBuffer[i] = this.linearToUlaw(pcmSample);
            }

            return returnAsBuffer ? ulawBuffer : ulawBuffer.toString('base64');
        } catch (error) {
            console.error('❌ Error in fallback PCM to μ-law conversion:', error);
            return base64Audio;
        }
    }

    /**
     * Converts PCM audio to Float32Array format for Gemini Live API
     * @param pcmBuffer - PCM audio buffer
     * @returns Float32Array suitable for Gemini
     */
    pcmToFloat32Array(pcmBuffer: Buffer): Float32Array {
        try {
            if (!pcmBuffer || !Buffer.isBuffer(pcmBuffer) || pcmBuffer.length === 0) {
                console.error('❌ Invalid PCM buffer');
                return new Float32Array(0);
            }

            const float32Array = new Float32Array(pcmBuffer.length / 2);
            for (let i = 0; i < float32Array.length; i++) {
                // Convert int16 to float32 (-1 to 1 range)
                float32Array[i] = pcmBuffer.readInt16LE(i * 2) / 32768.0;
            }
            return float32Array;
        } catch (error) {
            console.error('❌ Error in PCM to Float32 conversion:', error);
            return new Float32Array(0);
        }
    }

    /**
     * Creates audio blob for Gemini API
     * @param data - Float32Array audio data at 8kHz
     * @returns Audio blob for Gemini at 16kHz
     */
    createGeminiAudioBlob(data: Float32Array): { data: string; mimeType: string } {
        // Upsample from 8kHz to 16kHz for Gemini
        const upsampled = this.upsample8kTo16k(data);

        const int16 = new Int16Array(upsampled.length);
        for (let i = 0; i < upsampled.length; i++) {
            // Convert float32 -1 to 1 to int16 -32768 to 32767
            int16[i] = upsampled[i] * 32768;
        }

        return {
            data: Buffer.from(int16.buffer).toString('base64'),
            mimeType: 'audio/pcm;rate=16000' // Gemini expects 16kHz for input
        };
    }

    /**
     * Enhanced resampling from 8kHz to 16kHz using cubic interpolation
     * @param data - Float32Array audio data at 8kHz
     * @returns Float32Array audio data at 16kHz
     */
    upsample8kTo16k(data: Float32Array): Float32Array {
        try {
            // Use cubic interpolation for better quality
            return this.resampleCubic(data, 2.0);
        } catch (error) {
            console.error('❌ Error in enhanced upsampling, falling back to linear:', error);
            return this.upsample8kTo16kLinear(data);
        }
    }

    /**
     * Fallback linear interpolation resampling from 8kHz to 16kHz
     * @param data - Float32Array audio data at 8kHz
     * @returns Float32Array audio data at 16kHz
     */
    private upsample8kTo16kLinear(data: Float32Array): Float32Array {
        const outputLength = data.length * 2; // Double the sample rate
        const output = new Float32Array(outputLength);

        for (let i = 0; i < data.length - 1; i++) {
            const current = data[i];
            const next = data[i + 1];

            // Original sample
            output[i * 2] = current;
            // Interpolated sample
            output[i * 2 + 1] = (current + next) / 2;
        }

        // Handle last sample
        if (data.length > 0) {
            output[outputLength - 2] = data[data.length - 1];
            output[outputLength - 1] = data[data.length - 1];
        }

        return output;
    }

    /**
     * Enhanced downsampling from 24kHz to 8kHz with anti-aliasing
     * @param data - Float32Array audio data at 24kHz
     * @returns Float32Array audio data at 8kHz
     */
    downsample24kTo8k(data: Float32Array): Float32Array {
        try {
            // Apply anti-aliasing filter before downsampling
            const filtered = this.applyAntiAliasingFilter(data, 24000, 4000);
            return this.resampleCubic(filtered, 8000 / 24000);
        } catch (error) {
            console.error('❌ Error in enhanced downsampling, falling back to simple:', error);
            return this.downsample24kTo8kSimple(data);
        }
    }

    /**
     * Simple decimation resampling from 24kHz to 8kHz (fallback)
     * @param data - Float32Array audio data at 24kHz
     * @returns Float32Array audio data at 8kHz
     */
    private downsample24kTo8kSimple(data: Float32Array): Float32Array {
        const outputLength = Math.floor(data.length / 3); // Divide by 3 (24k/8k = 3)
        const output = new Float32Array(outputLength);

        for (let i = 0; i < outputLength; i++) {
            // Take every 3rd sample (simple decimation)
            output[i] = data[i * 3];
        }

        return output;
    }

    /**
     * Cubic interpolation resampling for better audio quality
     * @param input - Input audio data
     * @param ratio - Resampling ratio (output_rate / input_rate)
     * @returns Resampled audio data
     */
    resampleCubic(input: Float32Array, ratio: number): Float32Array {
        const outputLength = Math.floor(input.length * ratio);
        const output = new Float32Array(outputLength);

        for (let i = 0; i < outputLength; i++) {
            const srcIndex = i / ratio;
            const srcIndexInt = Math.floor(srcIndex);
            const fraction = srcIndex - srcIndexInt;

            // Get 4 points for cubic interpolation
            const y0 = input[Math.max(0, srcIndexInt - 1)] || 0;
            const y1 = input[srcIndexInt] || 0;
            const y2 = input[Math.min(input.length - 1, srcIndexInt + 1)] || 0;
            const y3 = input[Math.min(input.length - 1, srcIndexInt + 2)] || 0;

            // Cubic interpolation
            const a = -0.5 * y0 + 1.5 * y1 - 1.5 * y2 + 0.5 * y3;
            const b = y0 - 2.5 * y1 + 2 * y2 - 0.5 * y3;
            const c = -0.5 * y0 + 0.5 * y2;
            const d = y1;

            output[i] = a * fraction * fraction * fraction + b * fraction * fraction + c * fraction + d;
        }

        return output;
    }

    /**
     * Apply anti-aliasing low-pass filter before downsampling
     * @param samples - Input audio samples
     * @param sampleRate - Input sample rate
     * @param cutoffFreq - Cutoff frequency for anti-aliasing
     * @returns Filtered audio samples
     */
    private applyAntiAliasingFilter(samples: Float32Array, sampleRate: number, cutoffFreq: number): Float32Array {
        const rc = 1.0 / (cutoffFreq * 2 * Math.PI);
        const dt = 1.0 / sampleRate;
        const alpha = dt / (rc + dt);

        const filtered = new Float32Array(samples.length);
        filtered[0] = samples[0];

        for (let i = 1; i < samples.length; i++) {
            filtered[i] = filtered[i-1] + alpha * (samples[i] - filtered[i-1]);
        }

        return filtered;
    }

    /**
     * Convert WebM audio to PCM16 format for Gemini Live API
     * @param webmData - WebM audio data from browser
     * @param targetSampleRate - Target sample rate (default: 16000)
     * @returns PCM16 audio buffer
     */
    static convertWebmToPCM16(webmData: Buffer, targetSampleRate = 16000): Buffer {
        try {
            audioLogger.debug('Converting WebM to PCM16', {
                inputSize: webmData.length,
                targetSampleRate: targetSampleRate
            });

            // Use Web Audio API to decode WebM data
            const audioBuffer = AudioConverter.decodeAudioData(webmData);
            
            console.log('🎵 Decoded WebM audio:', {
                sampleRate: audioBuffer.sampleRate,
                numberOfChannels: audioBuffer.numberOfChannels,
                length: audioBuffer.length,
                duration: audioBuffer.duration
            });

            // Convert to PCM16 at target sample rate
            const pcm16Buffer = AudioConverter.convertToPCM16(audioBuffer, targetSampleRate);
            
            console.log('🎵 PCM16 conversion result:', {
                outputSize: pcm16Buffer.length,
                targetSampleRate: targetSampleRate
            });

            return pcm16Buffer;
        } catch (error) {
            console.error('❌ Error converting WebM to PCM16:', error);
            throw error;
        }
    }

    /**
     * Decode WebM audio data using Web Audio API
     * @param webmData - WebM audio data
     * @returns Decoded audio buffer
     */
    static decodeAudioData(webmData: Buffer): MockAudioBuffer {
        // WebM container typically contains Opus-encoded audio
        // For browser audio, we expect PCM data, not WebM
        // The browser should send raw PCM audio data, not WebM-encoded data
        
        // Check if this is actually PCM data mislabeled as WebM
        // Browser MediaRecorder with mimeType 'audio/webm' still sends PCM chunks
        if (webmData.length > 0 && webmData.length % 2 === 0) {
            // Treat as raw PCM16 data at 48kHz (browser default)
            const mockAudioBuffer: MockAudioBuffer = {
                sampleRate: 48000, // Browser recording default
                numberOfChannels: 1,
                length: webmData.length / 2, // 16-bit samples
                duration: 0,
                getChannelData: function(_channel: number): Float32Array {
                    // Convert PCM16 buffer to Float32Array
                    const samples = new Float32Array(this.length);
                    for (let i = 0; i < this.length; i++) {
                        // Read PCM16 sample and convert to float [-1, 1]
                        const sampleIndex = i * 2;
                        if (sampleIndex < webmData.length - 1) {
                            const sample = webmData.readInt16LE(sampleIndex) / 32768.0;
                            samples[i] = sample;
                        }
                    }
                    return samples;
                }
            };
            
            mockAudioBuffer.duration = mockAudioBuffer.length / mockAudioBuffer.sampleRate;
            return mockAudioBuffer;
        }
        
        // If not PCM, we need proper WebM decoding
        throw new Error('WebM container decoding not implemented. Expected PCM audio data.');
    }

    /**
     * Convert AudioBuffer to PCM16 at target sample rate
     * @param audioBuffer - Source audio buffer
     * @param targetSampleRate - Target sample rate
     * @returns PCM16 buffer
     */
    static convertToPCM16(audioBuffer: MockAudioBuffer, targetSampleRate: number): Buffer {
        try {
            // Get channel data (use first channel for mono)
            const sourceData = audioBuffer.getChannelData(0);
            const sourceSampleRate = audioBuffer.sampleRate;
            
            console.log('🎵 Converting to PCM16:', {
                sourceSampleRate: sourceSampleRate,
                targetSampleRate: targetSampleRate,
                sourceLength: sourceData.length
            });

            // Resample if needed
            let resampledData: Float32Array;
            if (sourceSampleRate !== targetSampleRate) {
                const resampleRatio = targetSampleRate / sourceSampleRate;
                console.log('🎵 Resampling with ratio:', resampleRatio);
                
                // Use the existing resampling function
                const converter = new AudioConverter();
                resampledData = converter.resampleCubic(sourceData, resampleRatio);
            } else {
                resampledData = sourceData;
            }

            console.log('🎵 Resampled data length:', resampledData.length);

            // Convert to Int16 PCM
            const pcm16Buffer = Buffer.alloc(resampledData.length * 2);
            for (let i = 0; i < resampledData.length; i++) {
                // Clamp and convert float32 to int16
                const sample = Math.max(-1, Math.min(1, resampledData[i]));
                const int16Sample = Math.round(sample * 32767);
                pcm16Buffer.writeInt16LE(int16Sample, i * 2);
            }

            return pcm16Buffer;
        } catch (error) {
            console.error('❌ Error converting to PCM16:', error);
            throw error;
        }
    }
}

// Export the convertWebmToPCM16 function globally for use in WebSocket handlers
export function convertWebmToPCM16(webmData: Buffer, targetSampleRate = 16000): Buffer {
    return AudioConverter.convertWebmToPCM16(webmData, targetSampleRate);
}