#!/bin/bash
# AI Code Audit Script - Enhanced version with file:line references

echo "🔍 Starting AI Code Audit with References..."
echo "Date: $(date)"
echo ""

echo "📁 Problematic Files:"
find . -name "*test*" -o -name "*debug*" -o -name "*temp*" | grep -v node_modules | grep -v audit-archive | while read file; do
    echo "  - $file"
done

echo ""
echo "📦 Import Anomalies:"
# Check for imports not in package.json
echo "  Checking for potentially missing dependencies..."
grep -Hn "from ['\"]@" --include="*.ts" --include="*.js" | grep -v node_modules | cut -d: -f1-2 | head -20

echo ""
echo "⚠️  TODO/FIXME Locations:"
grep -Hn "TODO\|FIXME\|HACK\|XXX" --include="*.ts" --include="*.js" | grep -v node_modules | head -20

echo ""
echo "🔄 Functions that might not exist:"
echo "  Checking for potential undefined function calls..."
grep -Hn "geminiSession\.send\|processAudio\|handleWebhook" --include="*.ts" | grep -v node_modules | head -10

echo ""
echo "⚡ Type Safety Issues:"
echo "  Any types:"
grep -Hn ": any\|as any" --include="*.ts" | grep -v node_modules | head -10
echo "  @ts-ignore usage:"
grep -Hn "@ts-ignore\|@ts-nocheck" --include="*.ts" | grep -v node_modules | head -10

echo ""
echo "🔐 Security Concerns:"
echo "  Authentication issues:"
grep -Hn "FORCE_AUTH\|NODE_ENV.*production" --include="*.ts" | grep -v node_modules | head -10
echo "  Hardcoded values:"
grep -Hn "localhost\|127\.0\.0\.1\|3000\|8080" --include="*.ts" | grep -v node_modules | grep -v "process\.env" | head -10

echo ""
echo "🏃 Race Condition Risks:"
echo "  Shared Maps/Sets:"
grep -Hn "new Map\|new Set" --include="*.ts" | grep -v node_modules | head -10
echo "  Static stores:"
grep -Hn "static.*Map\|static.*Set\|static.*Store" --include="*.ts" | grep -v node_modules | head -10

echo ""
echo "🔥 Empty Catch Blocks:"
grep -A2 -Hn "catch\s*(" --include="*.ts" | grep -v node_modules | grep -A1 "{\s*$" | grep -B1 "}" | head -20

echo ""
echo "✅ Audit complete! References ready for fixes."